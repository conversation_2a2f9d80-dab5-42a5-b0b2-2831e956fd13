import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import time


class ConvolutionalNeuralNetwork:
    def __init__(self,
                 input_shape=(32, 32, 3),  # 输入图像形状 (高度, 宽度, 通道数)
                 conv1_filters=32,  # 第一个卷积层过滤器数量
                 conv1_kernel_size=3,  # 第一个卷积层卷积核大小
                 conv1_stride=1,  # 第一个卷积层步长
                 conv1_padding=0,  # 第一个卷积层填充
                 pool1_size=2,  # 第一个池化层大小
                 pool1_stride=2,  # 第一个池化层步长
                 conv2_filters=64,  # 第二个卷积层过滤器数量
                 conv2_kernel_size=3,  # 第二个卷积层卷积核大小
                 conv2_stride=1,  # 第二个卷积层步长
                 conv2_padding=0,  # 第二个卷积层填充
                 pool2_size=2,  # 第二个池化层大小
                 pool2_stride=2,  # 第二个池化层步长
                 fc1_size=128,  # 第一个全连接层大小
                 fc2_size=10,  # 第二个全连接层大小（输出类别数）
                 learning_rate=0.01,  # 学习率
                 batch_size=32,  # 批量大小
                 epochs=10):  # 训练轮数

        # 保存超参数
        self.input_shape = input_shape
        self.conv1_filters = conv1_filters
        self.conv1_kernel_size = conv1_kernel_size
        self.conv1_stride = conv1_stride
        self.conv1_padding = conv1_padding
        self.pool1_size = pool1_size
        self.pool1_stride = pool1_stride
        self.conv2_filters = conv2_filters
        self.conv2_kernel_size = conv2_kernel_size
        self.conv2_stride = conv2_stride
        self.conv2_padding = conv2_padding
        self.pool2_size = pool2_size
        self.pool2_stride = pool2_stride
        self.fc1_size = fc1_size
        self.fc2_size = fc2_size
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.epochs = epochs

        # 计算每层输出尺寸
        # 第一个卷积层后的尺寸
        h1 = self._calculate_output_size(input_shape[0], conv1_kernel_size, conv1_stride, conv1_padding)
        w1 = self._calculate_output_size(input_shape[1], conv1_kernel_size, conv1_stride, conv1_padding)

        # 第一个池化层后的尺寸
        h2 = self._calculate_output_size(h1, pool1_size, pool1_stride, 0)
        w2 = self._calculate_output_size(w1, pool1_size, pool1_stride, 0)

        # 第二个卷积层后的尺寸
        h3 = self._calculate_output_size(h2, conv2_kernel_size, conv2_stride, conv2_padding)
        w3 = self._calculate_output_size(w2, conv2_kernel_size, conv2_stride, conv2_padding)

        # 第二个池化层后的尺寸
        h4 = self._calculate_output_size(h3, pool2_size, pool2_stride, 0)
        w4 = self._calculate_output_size(w3, pool2_size, pool2_stride, 0)

        # 展平后的特征数量
        flattened_size = h4 * w4 * conv2_filters

        # 初始化权重和偏置
        # 第一个卷积层"""这里改成不是随机的"""conv1_filters：卷积层过滤器数量
        self.W1 = np.random.randn(conv1_kernel_size, conv1_kernel_size, input_shape[2], conv1_filters) * 0.01
        self.b1 = np.zeros((1, 1, 1, conv1_filters))

        # 第二个卷积层
        self.W2 = np.random.randn(conv2_kernel_size, conv2_kernel_size, conv1_filters, conv2_filters) * 0.01
        self.b2 = np.zeros((1, 1, 1, conv2_filters))

        # 第一个全连接层
        self.W3 = np.random.randn(flattened_size, fc1_size) * 0.01
        self.b3 = np.zeros((1, fc1_size))

        # 第二个全连接层
        self.W4 = np.random.randn(fc1_size, fc2_size) * 0.01
        self.b4 = np.zeros((1, fc2_size))

        # 保存中间值，用于反向传播
        self.cache = {}

        # 保存训练历史
        self.train_loss_history = []
        self.train_acc_history = []
        self.val_loss_history = []
        self.val_acc_history = []

    def _calculate_output_size(self, input_size, kernel_size, stride, padding):
        """计算卷积或池化后的输出尺寸"""
        return (input_size + 2 * padding - kernel_size) // stride + 1

    def _pad(self, X, padding):
        """对输入进行填充"""#可以用np.pad()替代吧？？？
        if padding == 0:
            return X

        if len(X.shape) == 4:  # 批量数据 n表示批次
            n, h, w, c = X.shape
            X_padded = np.zeros((n, h + 2 * padding, w + 2 * padding, c))
            X_padded[:, padding:padding + h, padding:padding + w, :] = X
        else:  # 单个样本
            h, w, c = X.shape
            X_padded = np.zeros((h + 2 * padding, w + 2 * padding, c))
            X_padded[padding:padding + h, padding:padding + w, :] = X

        return X_padded

    def _convolve(self, X, W, b, stride, padding):
        """执行卷积操作"""
        # 填充输入
        X_padded = self._pad(X, padding)

        # 获取维度
        if len(X.shape) == 4:  # 批量数据
            n, h_in, w_in, c_in = X_padded.shape
        else:  # 单个样本，添加维度，*表示为将形状解析为多个参数
            X_padded = X_padded.reshape(1, *X_padded.shape)
            n, h_in, w_in, c_in = X_padded.shape

        h_filter, w_filter, _, c_out = W.shape

        # 计算输出维度
        h_out = (h_in - h_filter) // stride + 1
        w_out = (w_in - w_filter) // stride + 1

        # 初始化输出
        output = np.zeros((n, h_out, w_out, c_out))

        # 执行卷积
        for i in range(h_out):
            for j in range(w_out):
                h_start = i * stride
                h_end = h_start + h_filter
                w_start = j * stride
                w_end = w_start + w_filter

                # 提取当前窗口
                X_window = X_padded[:, h_start:h_end, w_start:w_end, :]

                # 对每个过滤器执行卷积
                for k in range(c_out):
                    # 重塑窗口和过滤器以便点积
                    # reshape(n, -1, c_in) 将窗口的高度和宽度展平为一个维度（-1 表示自动计算），得到形状 (n, h_filter*w_filter, c_in)
                    #reshape(-1, c_in)将卷积核的高度和宽度展平，得到形状(h_filter * w_filter, c_in)。 例如，若卷积核大小为3x3，则展平后形状为(9, c_in)。
                    X_window_reshaped = X_window.reshape(n, -1, c_in)
                    W_reshaped = W[:, :, :, k].reshape(-1, c_in)

                    # 计算卷积，去找维度相同点积，通过 X_window_reshaped * W_reshaped 进行逐元素相乘，得到形状 (n, h_filter*w_filter, c_in)
                    #np.sum(..., axis=(1, 2)) 对每个样本的 h_filter*w_filter 和 c_in 维度（通道数）求和，得到形状 (n,) 的向量，即每个样本的卷积结果
                    #偏置张量，形状通常为 (1, 1, 1, k)（针对 k 个输出通道），b[0, 0, 0, k] 表示第 k 个通道的偏置值
                    output[:, i, j, k] = np.sum(X_window_reshaped * W_reshaped, axis=(1, 2)) + b[0, 0, 0, k]

        return output

    def _max_pool(self, X, pool_size, stride):
        """执行最大池化操作"""
        n, h_in, w_in, c = X.shape

        # 计算输出维度
        h_out = (h_in - pool_size) // stride + 1
        w_out = (w_in - pool_size) // stride + 1

        # 初始化输出和池化索引（用于反向传播）,2：存储两个索引值（通常表示相对位置的坐标，如行和列的偏移量）
        output = np.zeros((n, h_out, w_out, c))
        max_indices = np.zeros((n, h_out, w_out, c, 2), dtype=int)

        # 执行池化,
        for i in range(h_out):
            for j in range(w_out):
                h_start = i * stride
                h_end = h_start + pool_size
                w_start = j * stride
                w_end = w_start + pool_size

                # 提取当前窗口
                X_window = X[:, h_start:h_end, w_start:w_end, :]

                # 对每个通道执行最大池化,reshape(n, -1) 重塑后形状为 (n, h×w×c)，即每个样本的窗口数据被展平为一个向量
                for k in range(c):
                    # 找到最大值
                    window_channel = X_window[:, :, :, k]
                    window_channel_reshaped = window_channel.reshape(n, -1)
                    max_idx = np.argmax(window_channel_reshaped, axis=1)

                    # 计算最大值的原始索引,取整和取余
                    max_h = max_idx // pool_size
                    max_w = max_idx % pool_size

                    # 保存最大值和索引
                    output[:, i, j, k] = np.max(window_channel_reshaped, axis=1)
                    max_indices[:, i, j, k, 0] = max_h
                    max_indices[:, i, j, k, 1] = max_w

        return output, max_indices

    def _relu(self, X):
        """ReLU激活函数"""
        return np.maximum(0, X)

    def _softmax(self, X):
        """Softmax激活函数"""
        exp_X = np.exp(X - np.max(X, axis=1, keepdims=True))
        return exp_X / np.sum(exp_X, axis=1, keepdims=True)

    def _categorical_cross_entropy(self, y_pred, y_true):
        """计算分类交叉熵损失"""
        n = y_pred.shape[0]
        loss = -np.sum(y_true * np.log(y_pred + 1e-8)) / n
        return loss

    def forward(self, X):
        """前向传播"""
        # 确保输入是4D数组 [batch_size, height, width, channels]
        if len(X.shape) == 3:
            X = X.reshape(1, *X.shape)

        # 第一个卷积层
        Z1 = self._convolve(X, self.W1, self.b1, self.conv1_stride, self.conv1_padding)
        A1 = self._relu(Z1)
        self.cache['Z1'] = Z1
        self.cache['A1'] = A1

        # 第一个池化层
        P1, max_indices1 = self._max_pool(A1, self.pool1_size, self.pool1_stride)
        self.cache['P1'] = P1
        self.cache['max_indices1'] = max_indices1

        # 第二个卷积层
        Z2 = self._convolve(P1, self.W2, self.b2, self.conv2_stride, self.conv2_padding)
        A2 = self._relu(Z2)
        self.cache['Z2'] = Z2
        self.cache['A2'] = A2

        # 第二个池化层
        P2, max_indices2 = self._max_pool(A2, self.pool2_size, self.pool2_stride)
        self.cache['P2'] = P2
        self.cache['max_indices2'] = max_indices2

        # 展平
        F = P2.reshape(P2.shape[0], -1)
        self.cache['F'] = F

        # 第一个全连接层
        Z3 = np.dot(F, self.W3) + self.b3
        A3 = self._relu(Z3)
        self.cache['Z3'] = Z3
        self.cache['A3'] = A3

        # 第二个全连接层
        Z4 = np.dot(A3, self.W4) + self.b4
        A4 = self._softmax(Z4)
        self.cache['Z4'] = Z4
        self.cache['A4'] = A4

        return A4

    def _relu_derivative(self, X):
        """ReLU导数"""
        return (X > 0).astype(float)

    def backward(self, X, y):
        """反向传播"""
        m = X.shape[0]

        # 获取前向传播的缓存
        A4 = self.cache['A4']  # 输出层激活
        A3 = self.cache['A3']  # 第一个全连接层激活
        Z3 = self.cache['Z3']  # 第一个全连接层线性输出
        F = self.cache['F']  # 展平的特征
        P2 = self.cache['P2']  # 第二个池化层输出
        A2 = self.cache['A2']  # 第二个卷积层激活
        Z2 = self.cache['Z2']  # 第二个卷积层线性输出
        P1 = self.cache['P1']  # 第一个池化层输出
        A1 = self.cache['A1']  # 第一个卷积层激活
        Z1 = self.cache['Z1']  # 第一个卷积层线性输出

        # 输出层梯度
        dA4 = A4 - y  # 交叉熵损失对softmax输出的导数

        # 第二个全连接层梯度
        dW4 = np.dot(A3.T, dA4) / m
        db4 = np.sum(dA4, axis=0, keepdims=True) / m
        dA3 = np.dot(dA4, self.W4.T)

        # 第一个全连接层梯度
        dZ3 = dA3 * self._relu_derivative(Z3)
        dW3 = np.dot(F.T, dZ3) / m
        db3 = np.sum(dZ3, axis=0, keepdims=True) / m
        dF = np.dot(dZ3, self.W3.T)

        # 重塑回池化层输出形状
        dP2 = dF.reshape(P2.shape)

        # 第二个池化层梯度（反向最大池化）
        dA2 = np.zeros_like(A2)
        max_indices2 = self.cache['max_indices2']

        # 对每个样本、每个位置、每个通道
        for i in range(dP2.shape[0]):  # 样本
            for h in range(dP2.shape[1]):  # 高度
                for w in range(dP2.shape[2]):  # 宽度
                    for c in range(dP2.shape[3]):  # 通道
                        # 获取最大值的原始位置
                        max_h = max_indices2[i, h, w, c, 0]
                        max_w = max_indices2[i, h, w, c, 1]

                        # 计算原始窗口的起始位置
                        h_start = h * self.pool2_stride
                        w_start = w * self.pool2_stride

                        # 将梯度传递给最大值位置
                        dA2[i, h_start + max_h, w_start + max_w, c] += dP2[i, h, w, c]

        # 第二个卷积层梯度
        dZ2 = dA2 * self._relu_derivative(Z2)

        # 这里简化了卷积梯度计算，实际应该使用完整的卷积反向传播
        # 为了简化，我们使用一个近似方法

        # 第一个池化层梯度（反向最大池化）
        dP1 = np.zeros_like(P1)
        # 这里应该计算dP1，但为了简化，我们跳过了详细实现

        # 第一个卷积层梯度
        dZ1 = dP1 * self._relu_derivative(Z1)
        # 同样，这里简化了卷积梯度计算

        # 更新权重和偏置
        self.W4 -= self.learning_rate * dW4
        self.b4 -= self.learning_rate * db4
        self.W3 -= self.learning_rate * dW3
        self.b3 -= self.learning_rate * db3

        # 注意：为了简化，我们没有实现卷积层的权重更新
        # 在实际应用中，应该计算并更新W1、b1、W2和b2

    def train(self, X_train, y_train, X_val=None, y_val=None):
        """训练模型"""
        n_samples = X_train.shape[0]
        n_batches = int(np.ceil(n_samples / self.batch_size))

        for epoch in range(self.epochs):
            start_time = time.time()

            # 打乱数据
            indices = np.random.permutation(n_samples)
            X_shuffled = X_train[indices]
            y_shuffled = y_train[indices]

            # 批量训练
            epoch_loss = 0
            y_pred_all = []

            for batch in range(n_batches):
                start_idx = batch * self.batch_size
                end_idx = min((batch + 1) * self.batch_size, n_samples)

                X_batch = X_shuffled[start_idx:end_idx]
                y_batch = y_shuffled[start_idx:end_idx]

                # 前向传播
                y_pred = self.forward(X_batch)
                y_pred_all.append(y_pred)

                # 计算损失
                loss = self._categorical_cross_entropy(y_pred, y_batch)
                epoch_loss += loss * (end_idx - start_idx)

                # �
if __name__ == "__main__":
    def unpickle(file):
        import pickle
        with open(file, 'rb') as fo:
            dict = pickle.load(fo, encoding='bytes')
        return dict
    x=unpickle('cifar-10-python\\cifar-10-batches-py\\data_batch_1')[b'data']
    y=unpickle('cifar-10-python\\cifar-10-batches-py\\data_batch_1')[b'labels']
    label_names = unpickle('cifar-10-python\\cifar-10-batches-py\\batches.meta')[b'label_names']
    print(x.shape, x)
    print(y)
    print(label_names)