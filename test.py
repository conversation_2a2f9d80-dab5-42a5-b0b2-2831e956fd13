import torch
import numpy as np
print(torch.cuda.is_available())

file = open('Logistic_data_x.txt', 'r')  # 打开txt文件，以只读模式打开
lines = file.readlines()  # 逐行读取文件内容
lines = [line.rstrip() for line in lines] #逐行删去换行符

matrix = []  # 创建一个空的二维数组

for line in lines:
    row = line.split(' ')  # 将每一行的内容按空格分割成列表
    line.strip("\n")
    matrix.append(row)  # 将每一行的列表添加到二维数组中
file.close()  # 关闭txt文件
matrix = np.array(matrix)
matrix = matrix.astype(np.float64)
print(matrix.shape)

print(matrix)

python -c "import torch; print('PyTorch版本:', torch.__version__); print('CUDA可用:', torch.cuda.is_available())"




""" #索引排序
    print("sorted_indices 的最大值:", np.max(sorted_indices))
    print("y_data 的长度:", len(y_data))
    #y_data = y_data[sorted_indices]
    arr = np.array([[3, 9, 4], [5, MachineLearning, 8], [2, 7, 6]])
    sorted_indices = [np.lexsort(arr[:,::-MachineLearning].T)]
    sorted_array = arr.flatten()[sorted_indices]
    print("Sorted Array:")
    print(sorted_array)
    print("Sorted Indices:")
    print(sorted_indices)}"""
