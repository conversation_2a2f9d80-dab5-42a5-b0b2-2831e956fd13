import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import numpy as np
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import seaborn as sns

class CustomizableCNN(nn.Module):
    def __init__(self,
                 # 卷积层1参数
                 conv1_filters=64, conv1_kernel_size=3, conv1_stride=1, conv1_padding=1,
                 # 池化层1参数
                 pool1_size=2, pool1_stride=2,
                 # 卷积层2参数
                 conv2_filters=128, conv2_kernel_size=3, conv2_stride=1, conv2_padding=1,
                 # 池化层2参数
                 pool2_size=2, pool2_stride=2,
                 # 全连接层参数
                 fc1_neurons=512, fc2_neurons=256,
                 # 其他参数
                 num_classes=10, dropout_rate=0.5):
        """
        可自定义的CNN模型

        参数说明:
        conv1_filters: 第一层卷积核数量
        conv1_kernel_size: 第一层卷积核大小
        conv1_stride: 第一层卷积步长
        conv1_padding: 第一层卷积填充
        pool1_size: 第一层池化大小
        pool1_stride: 第一层池化步长
        conv2_filters: 第二层卷积核数量
        conv2_kernel_size: 第二层卷积核大小
        conv2_stride: 第二层卷积步长
        conv2_padding: 第二层卷积填充
        pool2_size: 第二层池化大小
        pool2_stride: 第二层池化步长
        fc1_neurons: 第一层全连接神经元数量
        fc2_neurons: 第二层全连接神经元数量
        num_classes: 分类数量
        dropout_rate: Dropout比率
        """
        super(CustomizableCNN, self).__init__()

        # 保存超参数
        self.conv1_filters = conv1_filters
        self.conv1_kernel_size = conv1_kernel_size
        self.conv2_filters = conv2_filters
        self.conv2_kernel_size = conv2_kernel_size
        self.fc1_neurons = fc1_neurons
        self.fc2_neurons = fc2_neurons

        # 卷积层1
        self.conv1 = nn.Conv2d(in_channels=3,
                              out_channels=conv1_filters,
                              kernel_size=conv1_kernel_size,
                              stride=conv1_stride,
                              padding=conv1_padding)
        self.bn1 = nn.BatchNorm2d(conv1_filters)

        # 池化层1
        self.pool1 = nn.MaxPool2d(kernel_size=pool1_size, stride=pool1_stride)

        # 卷积层2
        self.conv2 = nn.Conv2d(in_channels=conv1_filters,
                              out_channels=conv2_filters,
                              kernel_size=conv2_kernel_size,
                              stride=conv2_stride,
                              padding=conv2_padding)
        self.bn2 = nn.BatchNorm2d(conv2_filters)

        # 池化层2
        self.pool2 = nn.MaxPool2d(kernel_size=pool2_size, stride=pool2_stride)

        # 计算全连接层输入尺寸
        self.fc_input_size = self._calculate_fc_input_size()

        # 全连接层
        self.fc1 = nn.Linear(self.fc_input_size, fc1_neurons)
        self.bn3 = nn.BatchNorm1d(fc1_neurons)
        self.dropout1 = nn.Dropout(dropout_rate)

        self.fc2 = nn.Linear(fc1_neurons, fc2_neurons)
        self.bn4 = nn.BatchNorm1d(fc2_neurons)
        self.dropout2 = nn.Dropout(dropout_rate)

        # 输出层
        self.fc3 = nn.Linear(fc2_neurons, num_classes)

        # 初始化权重
        self._initialize_weights()

        print(f"CNN模型架构:")
        print(f"  Conv1: {conv1_filters}个{conv1_kernel_size}×{conv1_kernel_size}卷积核")
        print(f"  Pool1: {pool1_size}×{pool1_size}最大池化")
        print(f"  Conv2: {conv2_filters}个{conv2_kernel_size}×{conv2_kernel_size}卷积核")
        print(f"  Pool2: {pool2_size}×{pool2_size}最大池化")
        print(f"  FC1: {fc1_neurons}个神经元")
        print(f"  FC2: {fc2_neurons}个神经元")
        print(f"  输出: {num_classes}个类别")
        print(f"  全连接输入尺寸: {self.fc_input_size}")

        # 计算总参数数量
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        print(f"  总参数数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")

    def _calculate_fc_input_size(self):
        """计算全连接层输入尺寸"""
        # CIFAR-10图像尺寸: 32x32
        h, w = 32, 32

        # 经过Conv1和Pool1
        h = (h + 2 * self.conv1.padding[0] - self.conv1.kernel_size[0]) // self.conv1.stride[0] + 1
        w = (w + 2 * self.conv1.padding[1] - self.conv1.kernel_size[1]) // self.conv1.stride[1] + 1
        h = (h - self.pool1.kernel_size) // self.pool1.stride + 1
        w = (w - self.pool1.kernel_size) // self.pool1.stride + 1

        # 经过Conv2和Pool2
        h = (h + 2 * self.conv2.padding[0] - self.conv2.kernel_size[0]) // self.conv2.stride[0] + 1
        w = (w + 2 * self.conv2.padding[1] - self.conv2.kernel_size[1]) // self.conv2.stride[1] + 1
        h = (h - self.pool2.kernel_size) // self.pool2.stride + 1
        w = (w - self.pool2.kernel_size) // self.pool2.stride + 1

        return h * w * self.conv2_filters

    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d) or isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        """前向传播"""
        # 卷积层1 + 批量归一化 + ReLU + 池化
        x = self.pool1(F.relu(self.bn1(self.conv1(x))))

        # 卷积层2 + 批量归一化 + ReLU + 池化
        x = self.pool2(F.relu(self.bn2(self.conv2(x))))

        # 展平
        x = x.view(x.size(0), -1)

        # 全连接层1 + 批量归一化 + ReLU + Dropout
        x = self.dropout1(F.relu(self.bn3(self.fc1(x))))

        # 全连接层2 + 批量归一化 + ReLU + Dropout
        x = self.dropout2(F.relu(self.bn4(self.fc2(x))))

        # 输出层
        x = self.fc3(x)

        return x


def load_cifar10_data(batch_size=128, num_workers=4):
    """
    加载CIFAR-10数据集
    """
    print("正在加载CIFAR-10数据集...")

    # 数据预处理和增强
    transform_train = transforms.Compose([
        transforms.RandomCrop(32, padding=4),  # 随机裁剪
        transforms.RandomHorizontalFlip(p=0.5),  # 随机水平翻转
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),  # 颜色抖动
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))  # CIFAR-10标准化
    ])

    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
    ])

    # 加载数据集
    trainset = torchvision.datasets.CIFAR10(root='./cifar-10-python', train=True,
                                           download=False, transform=transform_train)
    trainloader = DataLoader(trainset, batch_size=batch_size, shuffle=True,
                           num_workers=num_workers, pin_memory=True)

    testset = torchvision.datasets.CIFAR10(root='./cifar-10-python', train=False,
                                          download=False, transform=transform_test)
    testloader = DataLoader(testset, batch_size=batch_size, shuffle=False,
                          num_workers=num_workers, pin_memory=True)

    # 验证集（从训练集中分离）
    train_size = int(0.9 * len(trainset))
    val_size = len(trainset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(trainset, [train_size, val_size])

    valloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False,
                         num_workers=num_workers, pin_memory=True)

    print(f"数据加载完成:")
    print(f"  训练集: {len(train_dataset):,} 样本")
    print(f"  验证集: {len(val_dataset):,} 样本")
    print(f"  测试集: {len(testset):,} 样本")
    print(f"  批次大小: {batch_size}")

    # CIFAR-10类别名称
    classes = ['airplane', 'automobile', 'bird', 'cat', 'deer',
              'dog', 'frog', 'horse', 'ship', 'truck']

    return trainloader, valloader, testloader, classes


def train_model(model, trainloader, valloader, device, epochs=50, learning_rate=0.001):
    """
    训练模型
    """
    print(f"\n开始训练模型...")
    print(f"设备: {device}")
    print(f"训练轮数: {epochs}")
    print(f"学习率: {learning_rate}")
    print("-" * 70)

    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-4)

    # 学习率调度器
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=15, gamma=0.8)

    # 训练历史
    train_losses = []
    train_accuracies = []
    val_losses = []
    val_accuracies = []
    epoch_times = []

    best_val_acc = 0.0
    patience = 10
    patience_counter = 0

    start_time = time.time()

    for epoch in range(epochs):
        epoch_start = time.time()

        # 训练阶段
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (inputs, targets) in enumerate(trainloader):
            inputs, targets = inputs.to(device), targets.to(device)

            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()

            running_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()

            # 打印进度
            if batch_idx % 100 == 0:
                print(f'Epoch {epoch+1}/{epochs}, Batch {batch_idx}/{len(trainloader)}, '
                      f'Loss: {loss.item():.4f}, Acc: {100.*correct/total:.2f}%')

        train_loss = running_loss / len(trainloader)
        train_acc = 100. * correct / total

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for inputs, targets in valloader:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)

                val_loss += loss.item()
                _, predicted = outputs.max(1)
                val_total += targets.size(0)
                val_correct += predicted.eq(targets).sum().item()

        val_loss /= len(valloader)
        val_acc = 100. * val_correct / val_total

        # 记录历史
        train_losses.append(train_loss)
        train_accuracies.append(train_acc)
        val_losses.append(val_loss)
        val_accuracies.append(val_acc)

        # 学习率调度
        scheduler.step()

        # 早停机制
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'best_cifar10_cnn.pth')
            print(f'  新的最佳验证准确率: {best_val_acc:.2f}%')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f'  早停：验证准确率连续{patience}轮未提升')
                break

        epoch_time = time.time() - epoch_start
        epoch_times.append(epoch_time)

        print(f'Epoch {epoch+1}/{epochs} - '
              f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}% - '
              f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}% - '
              f'Time: {epoch_time:.2f}s - LR: {scheduler.get_last_lr()[0]:.6f}')

    total_time = time.time() - start_time

    print("-" * 70)
    print(f"训练完成！")
    print(f"总训练时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
    print(f"平均每轮时间: {np.mean(epoch_times):.2f}秒")
    print(f"最佳验证准确率: {best_val_acc:.2f}%")

    return {
        'train_losses': train_losses,
        'train_accuracies': train_accuracies,
        'val_losses': val_losses,
        'val_accuracies': val_accuracies,
        'epoch_times': epoch_times,
        'total_time': total_time,
        'best_val_acc': best_val_acc
    }


def evaluate_model(model, testloader, device, classes):
    """
    评估模型性能
    """
    print("\n正在评估模型...")

    # 加载最佳模型
    model.load_state_dict(torch.load('best_cifar10_cnn.pth'))
    model.eval()

    all_predictions = []
    all_targets = []
    all_probabilities = []

    start_time = time.time()

    with torch.no_grad():
        for inputs, targets in testloader:
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            probabilities = F.softmax(outputs, dim=1)
            _, predicted = outputs.max(1)

            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())

    eval_time = time.time() - start_time

    # 计算各种指标
    accuracy = accuracy_score(all_targets, all_predictions)

    # 分类报告
    report = classification_report(all_targets, all_predictions,
                                 target_names=classes, output_dict=True)

    # 混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions)

    # Top-k准确率
    all_probabilities = np.array(all_probabilities)
    top2_acc = top_k_accuracy(all_probabilities, all_targets, k=2)
    top3_acc = top_k_accuracy(all_probabilities, all_targets, k=3)

    print(f"评估完成，耗时: {eval_time:.2f}秒")
    print(f"测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"Top-2准确率: {top2_acc:.4f} ({top2_acc*100:.2f}%)")
    print(f"Top-3准确率: {top3_acc:.4f} ({top3_acc*100:.2f}%)")

    return {
        'accuracy': accuracy,
        'top2_accuracy': top2_acc,
        'top3_accuracy': top3_acc,
        'classification_report': report,
        'confusion_matrix': cm,
        'predictions': all_predictions,
        'targets': all_targets,
        'probabilities': all_probabilities,
        'eval_time': eval_time
    }


def top_k_accuracy(probabilities, targets, k=2):
    """计算Top-k准确率"""
    top_k_predictions = np.argsort(probabilities, axis=1)[:, -k:]
    correct = 0
    for i, true_label in enumerate(targets):
        if true_label in top_k_predictions[i]:
            correct += 1
    return correct / len(targets)


def plot_training_results(history, metrics, classes):
    """
    绘制训练结果和评估指标
    """
    fig = plt.figure(figsize=(20, 15))

    # 1. 训练损失
    ax1 = plt.subplot(3, 4, 1)
    plt.plot(history['train_losses'], label='Training Loss', linewidth=2, color='blue')
    plt.plot(history['val_losses'], label='Validation Loss', linewidth=2, color='red')
    plt.title('Model Loss', fontsize=14, fontweight='bold')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. 训练准确率
    ax2 = plt.subplot(3, 4, 2)
    plt.plot(history['train_accuracies'], label='Training Accuracy', linewidth=2, color='blue')
    plt.plot(history['val_accuracies'], label='Validation Accuracy', linewidth=2, color='red')
    plt.title('Model Accuracy', fontsize=14, fontweight='bold')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. 训练时间
    ax3 = plt.subplot(3, 4, 3)
    plt.bar(range(1, len(history['epoch_times']) + 1), history['epoch_times'], alpha=0.7, color='green')
    plt.title('Training Time per Epoch', fontsize=14, fontweight='bold')
    plt.xlabel('Epoch')
    plt.ylabel('Time (seconds)')
    plt.grid(True, alpha=0.3)

    # 4. 各类别精确率
    ax4 = plt.subplot(3, 4, 4)
    precision_scores = [metrics['classification_report'][cls]['precision'] for cls in classes]
    bars = plt.bar(range(len(classes)), precision_scores, alpha=0.7, color='orange')
    plt.title('Precision per Class', fontsize=14, fontweight='bold')
    plt.xlabel('Class')
    plt.ylabel('Precision')
    plt.xticks(range(len(classes)), [cls[:4] for cls in classes], rotation=45)
    plt.grid(True, alpha=0.3)

    # 5. 各类别召回率
    ax5 = plt.subplot(3, 4, 5)
    recall_scores = [metrics['classification_report'][cls]['recall'] for cls in classes]
    bars = plt.bar(range(len(classes)), recall_scores, alpha=0.7, color='purple')
    plt.title('Recall per Class', fontsize=14, fontweight='bold')
    plt.xlabel('Class')
    plt.ylabel('Recall')
    plt.xticks(range(len(classes)), [cls[:4] for cls in classes], rotation=45)
    plt.grid(True, alpha=0.3)

    # 6. 各类别F1分数
    ax6 = plt.subplot(3, 4, 6)
    f1_scores = [metrics['classification_report'][cls]['f1-score'] for cls in classes]
    bars = plt.bar(range(len(classes)), f1_scores, alpha=0.7, color='brown')
    plt.title('F1-Score per Class', fontsize=14, fontweight='bold')
    plt.xlabel('Class')
    plt.ylabel('F1-Score')
    plt.xticks(range(len(classes)), [cls[:4] for cls in classes], rotation=45)
    plt.grid(True, alpha=0.3)

    # 7. 混淆矩阵
    ax7 = plt.subplot(3, 4, (7, 8))
    cm = metrics['confusion_matrix']
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

    sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=[cls[:4] for cls in classes],
                yticklabels=[cls[:4] for cls in classes])
    plt.title('Confusion Matrix (%)', fontsize=14, fontweight='bold')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')

    # 8. 性能指标总结
    ax8 = plt.subplot(3, 4, 9)
    metrics_names = ['Accuracy', 'Top-2 Acc', 'Top-3 Acc', 'Macro Avg F1']
    metrics_values = [
        metrics['accuracy'],
        metrics['top2_accuracy'],
        metrics['top3_accuracy'],
        metrics['classification_report']['macro avg']['f1-score']
    ]

    bars = plt.bar(range(len(metrics_names)), metrics_values, alpha=0.7, color='teal')
    plt.title('Overall Performance Metrics', fontsize=14, fontweight='bold')
    plt.ylabel('Score')
    plt.xticks(range(len(metrics_names)), metrics_names, rotation=45)
    plt.grid(True, alpha=0.3)

    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', fontsize=10)

    # 9. GPU使用情况（如果可用）
    ax9 = plt.subplot(3, 4, 10)
    plt.axis('off')

    device_info = "GPU信息:\n"
    if torch.cuda.is_available():
        device_info += f"设备: {torch.cuda.get_device_name()}\n"
        device_info += f"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\n"
        device_info += f"CUDA版本: {torch.version.cuda}\n"
    else:
        device_info += "使用CPU训练"

    plt.text(0.1, 0.9, device_info, transform=ax9.transAxes, fontsize=12,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 10. 模型架构信息
    ax10 = plt.subplot(3, 4, (11, 12))
    plt.axis('off')

    architecture_text = f"""
    🏗️ CNN模型架构总结

    📊 性能指标:
    • 测试准确率: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)
    • Top-2准确率: {metrics['top2_accuracy']:.4f}
    • Top-3准确率: {metrics['top3_accuracy']:.4f}
    • 宏平均F1: {metrics['classification_report']['macro avg']['f1-score']:.4f}

    ⏱️ 训练信息:
    • 总训练时间: {history['total_time']/60:.2f} 分钟
    • 平均每轮时间: {np.mean(history['epoch_times']):.2f} 秒
    • 最佳验证准确率: {history['best_val_acc']:.2f}%
    • 训练轮数: {len(history['epoch_times'])}

    🚀 优化技术:
    • 批量归一化 ✓
    • Dropout正则化 ✓
    • 数据增强 ✓
    • 学习率调度 ✓
    • 早停机制 ✓
    • 梯度裁剪 ✓
    • GPU加速 ✓
    """

    plt.text(0.05, 0.95, architecture_text, transform=ax10.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

    plt.tight_layout()
    plt.savefig('cifar10_cnn_pytorch_results.png', dpi=150, bbox_inches='tight')
    plt.show()


def display_detailed_metrics(metrics, classes):
    """显示详细的评估指标"""
    print("\n" + "="*80)
    print("🏆 详细评估指标")
    print("="*80)

    print(f"\n📊 总体性能:")
    print(f"  • 测试准确率: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
    print(f"  • Top-2准确率: {metrics['top2_accuracy']:.4f} ({metrics['top2_accuracy']*100:.2f}%)")
    print(f"  • Top-3准确率: {metrics['top3_accuracy']:.4f} ({metrics['top3_accuracy']*100:.2f}%)")

    report = metrics['classification_report']
    print(f"  • 宏平均精确率: {report['macro avg']['precision']:.4f}")
    print(f"  • 宏平均召回率: {report['macro avg']['recall']:.4f}")
    print(f"  • 宏平均F1分数: {report['macro avg']['f1-score']:.4f}")
    print(f"  • 加权平均F1分数: {report['weighted avg']['f1-score']:.4f}")

    print(f"\n📋 各类别详细指标:")
    print("-" * 80)
    print(f"{'类别':<12} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'支持数':<8}")
    print("-" * 80)

    for class_name in classes:
        if class_name in report:
            precision = report[class_name]['precision']
            recall = report[class_name]['recall']
            f1 = report[class_name]['f1-score']
            support = report[class_name]['support']
            print(f"{class_name:<12} {precision:<8.3f} {recall:<8.3f} {f1:<8.3f} {support:<8}")

    print("-" * 80)
    print(f"{'平均/总计':<12} {report['macro avg']['precision']:<8.3f} {report['macro avg']['recall']:<8.3f} {report['macro avg']['f1-score']:<8.3f} {report['macro avg']['support']:<8}")

    # 找出表现最好和最差的类别
    f1_scores = [report[cls]['f1-score'] for cls in classes if cls in report]
    best_class_idx = np.argmax(f1_scores)
    worst_class_idx = np.argmin(f1_scores)

    print(f"\n🏆 表现最好的类别: {classes[best_class_idx]} (F1: {f1_scores[best_class_idx]:.3f})")
    print(f"📉 表现最差的类别: {classes[worst_class_idx]} (F1: {f1_scores[worst_class_idx]:.3f})")

    # 90%准确率分析
    if metrics['accuracy'] >= 0.90:
        print(f"\n🎉 恭喜！成功达到90%准确率目标！")
        print(f"   实际准确率: {metrics['accuracy']*100:.2f}%")
        print(f"   超出目标: {(metrics['accuracy']-0.90)*100:.2f}%")
    else:
        gap = 0.90 - metrics['accuracy']
        print(f"\n📈 距离90%目标还差: {gap*100:.2f}%")
        print(f"   当前准确率: {metrics['accuracy']*100:.2f}%")


if __name__ == "__main__":
    print("="*90)
    print("🚀 PyTorch CIFAR-10 CNN分类器")
    print("="*90)
    print("🎯 目标：使用PyTorch框架达到90%以上准确率")
    print("🔧 特性：GPU加速、可自定义超参数、全数据集训练、高计算效率")

    # 检查GPU可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n🖥️  计算设备: {device}")

    if torch.cuda.is_available():
        print(f"   GPU型号: {torch.cuda.get_device_name()}")
        print(f"   显存大小: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print(f"   CUDA版本: {torch.version.cuda}")
        print(f"   PyTorch版本: {torch.__version__}")
    else:
        print("   注意：未检测到GPU，将使用CPU训练（速度较慢）")

    # 1. 加载数据
    print("\n" + "="*60)
    print("步骤1: 加载CIFAR-10数据集")
    print("="*60)

    try:
        trainloader, valloader, testloader, classes = load_cifar10_data(
            batch_size=128,      # 批次大小
            num_workers=4        # 数据加载线程数
        )
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        print("请确保CIFAR-10数据集已正确下载到 './cifar-10-python' 目录")
        exit(1)

    # 2. 创建模型
    print("\n" + "="*60)
    print("步骤2: 创建可自定义CNN模型")
    print("="*60)

    # 🔧 可自定义的超参数配置
    model = CustomizableCNN(
        # 卷积层1参数
        conv1_filters=64,        # 第一层卷积核数量
        conv1_kernel_size=3,     # 第一层卷积核大小
        conv1_stride=1,          # 第一层卷积步长
        conv1_padding=1,         # 第一层卷积填充

        # 池化层1参数
        pool1_size=2,            # 第一层池化大小
        pool1_stride=2,          # 第一层池化步长

        # 卷积层2参数
        conv2_filters=128,       # 第二层卷积核数量
        conv2_kernel_size=3,     # 第二层卷积核大小
        conv2_stride=1,          # 第二层卷积步长
        conv2_padding=1,         # 第二层卷积填充

        # 池化层2参数
        pool2_size=2,            # 第二层池化大小
        pool2_stride=2,          # 第二层池化步长

        # 全连接层参数
        fc1_neurons=512,         # 第一层全连接神经元数量
        fc2_neurons=256,         # 第二层全连接神经元数量

        # 其他参数
        num_classes=10,          # 分类数量
        dropout_rate=0.5         # Dropout比率
    ).to(device)

    print(f"\n✅ 模型已创建并移动到 {device}")

    # 3. 训练模型
    print("\n" + "="*60)
    print("步骤3: 开始训练模型")
    print("="*60)

    history = train_model(
        model=model,
        trainloader=trainloader,
        valloader=valloader,
        device=device,
        epochs=50,               # 训练轮数
        learning_rate=0.001      # 学习率
    )

    # 4. 评估模型
    print("\n" + "="*60)
    print("步骤4: 评估模型性能")
    print("="*60)

    metrics = evaluate_model(model, testloader, device, classes)

    # 5. 显示最终结果
    print("\n" + "="*90)
    print("🏆 最终结果总结")
    print("="*90)

    accuracy_percent = metrics['accuracy'] * 100
    print(f"🎯 测试准确率: {metrics['accuracy']:.4f} ({accuracy_percent:.2f}%)")
    print(f"🥈 Top-2准确率: {metrics['top2_accuracy']:.4f} ({metrics['top2_accuracy']*100:.2f}%)")
    print(f"🥉 Top-3准确率: {metrics['top3_accuracy']:.4f} ({metrics['top3_accuracy']*100:.2f}%)")
    print(f"⏱️  总训练时间: {history['total_time']:.2f}秒 ({history['total_time']/60:.2f}分钟)")
    print(f"📊 平均每轮时间: {np.mean(history['epoch_times']):.2f}秒")
    print(f"🚀 最佳验证准确率: {history['best_val_acc']:.2f}%")

    # 90%准确率目标检查
    if metrics['accuracy'] >= 0.90:
        print(f"\n🎉 恭喜！成功达到90%准确率目标！")
        print(f"   实际准确率: {accuracy_percent:.2f}%")
        print(f"   超出目标: {accuracy_percent - 90:.2f}%")
        print(f"   🏅 优秀表现！")
    elif metrics['accuracy'] >= 0.85:
        print(f"\n📈 接近目标！当前准确率: {accuracy_percent:.2f}%")
        print(f"   距离90%目标还差: {90 - accuracy_percent:.2f}%")
        print(f"   💪 继续努力！")
    else:
        print(f"\n📊 当前准确率: {accuracy_percent:.2f}%")
        print(f"   距离90%目标还差: {90 - accuracy_percent:.2f}%")
        print(f"   🔧 建议调整超参数或增加训练轮数")

    # 6. 显示详细评估指标
    display_detailed_metrics(metrics, classes)

    # 7. 生成可视化结果
    print("\n" + "="*60)
    print("步骤5: 生成可视化结果")
    print("="*60)

    try:
        plot_training_results(history, metrics, classes)
        print("✅ 可视化结果已生成并保存为 'cifar10_cnn_pytorch_results.png'")
    except Exception as e:
        print(f"⚠️  可视化生成失败: {e}")
        print("这可能是由于缺少seaborn库，但不影响模型性能")

    # 8. 技术总结
    print(f"\n" + "="*90)
    print("🔧 技术实现总结")
    print("="*90)

    print(f"📐 网络架构:")
    print(f"   • 卷积层1: {model.conv1_filters}个{model.conv1_kernel_size}×{model.conv1_kernel_size}卷积核")
    print(f"   • 池化层1: {model.pool1.kernel_size}×{model.pool1.kernel_size}最大池化")
    print(f"   • 卷积层2: {model.conv2_filters}个{model.conv2_kernel_size}×{model.conv2_kernel_size}卷积核")
    print(f"   • 池化层2: {model.pool2.kernel_size}×{model.pool2.kernel_size}最大池化")
    print(f"   • 全连接层1: {model.fc1_neurons}个神经元")
    print(f"   • 全连接层2: {model.fc2_neurons}个神经元")
    print(f"   • 输出层: 10个神经元（CIFAR-10类别）")

    print(f"\n🚀 PyTorch优化技术:")
    print(f"   ✅ GPU并行计算加速")
    print(f"   ✅ 自动梯度计算")
    print(f"   ✅ 批量归一化")
    print(f"   ✅ Dropout正则化")
    print(f"   ✅ 数据增强（随机裁剪、翻转、颜色抖动）")
    print(f"   ✅ 学习率调度")
    print(f"   ✅ 早停机制")
    print(f"   ✅ 梯度裁剪")
    print(f"   ✅ 权重初始化（Kaiming初始化）")
    print(f"   ✅ 内存固定（pin_memory）")
    print(f"   ✅ 多线程数据加载")

    print(f"\n📈 评估指标:")
    print(f"   • 准确率 (Accuracy): {metrics['accuracy']:.4f}")
    print(f"   • Top-2准确率: {metrics['top2_accuracy']:.4f}")
    print(f"   • Top-3准确率: {metrics['top3_accuracy']:.4f}")
    print(f"   • 宏平均F1分数: {metrics['classification_report']['macro avg']['f1-score']:.4f}")
    print(f"   • 加权平均F1分数: {metrics['classification_report']['weighted avg']['f1-score']:.4f}")
    print(f"   • 各类别详细指标")
    print(f"   • 混淆矩阵分析")

    print(f"\n💾 模型保存:")
    print(f"   • 最佳模型已保存为: 'best_cifar10_cnn.pth'")
    print(f"   • 可视化结果已保存为: 'cifar10_cnn_pytorch_results.png'")

    print(f"\n🎯 性能优势:")
    if torch.cuda.is_available():
        print(f"   • GPU加速训练，速度提升10-100倍")
        print(f"   • 支持大批量并行计算")
        print(f"   • 自动内存管理")
    print(f"   • 全数据集训练（50,000训练样本）")
    print(f"   • 高效的数据预处理管道")
    print(f"   • 实时训练监控")
    print(f"   • 自动权重更新和优化")

    print("\n" + "="*90)
    print("🎊 PyTorch CIFAR-10 CNN分类器运行完成！")
    print("="*90)

    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("🧹 GPU内存已清理")
