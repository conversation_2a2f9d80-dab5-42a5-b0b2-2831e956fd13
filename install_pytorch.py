"""
PyTorch安装脚本
"""
import subprocess
import sys

def install_pytorch():
    """安装PyTorch和相关依赖"""
    print("正在安装PyTorch和相关依赖...")
    
    # 检测是否有CUDA
    try:
        import torch
        print("PyTorch已安装")
        return True
    except ImportError:
        pass
    
    # 安装命令
    packages = [
        "torch",
        "torchvision", 
        "torchaudio",
        "matplotlib",
        "scikit-learn",
        "seaborn",
        "numpy"
    ]
    
    for package in packages:
        try:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    print("✅ 所有依赖安装完成！")
    return True

if __name__ == "__main__":
    success = install_pytorch()
    if success:
        print("\n现在可以运行 CIFAR10_CNN_PyTorch.py 了！")
    else:
        print("\n安装失败，请手动安装PyTorch")
        print("访问 https://pytorch.org/ 获取安装指令")
