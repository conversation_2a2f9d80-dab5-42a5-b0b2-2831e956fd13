import numpy as np
import matplotlib.pyplot as plt
import pickle
import tarfile
import os
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import seaborn as sns

class ConvolutionalNeuralNetwork:
    def __init__(self, input_shape=(32, 32, 3), num_classes=10, learning_rate=0.001):
        """
        初始化卷积神经网络

        参数:
        input_shape -- 输入图像形状 (height, width, channels)
        num_classes -- 分类类别数
        learning_rate -- 学习率
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.learning_rate = learning_rate

        # 初始化网络参数
        self._initialize_parameters()

        # 训练历史
        self.train_loss_history = []
        self.train_acc_history = []
        self.val_loss_history = []
        self.val_acc_history = []

    def _initialize_parameters(self):
        """初始化网络参数"""
        # 卷积层1: 32个3x3卷积核
        self.conv1_filters = np.random.randn(3, 3, 3, 32) * 0.1
        self.conv1_bias = np.zeros((32,))

        # 卷积层2: 64个3x3卷积核
        self.conv2_filters = np.random.randn(3, 3, 32, 64) * 0.1
        self.conv2_bias = np.zeros((64,))

        # 全连接层参数（需要根据卷积后的尺寸计算）
        # 假设经过两次卷积和池化后，特征图尺寸为8x8x64
        self.fc1_weights = np.random.randn(8 * 8 * 64, 128) * 0.1
        self.fc1_bias = np.zeros((128,))

        self.fc2_weights = np.random.randn(128, self.num_classes) * 0.1
        self.fc2_bias = np.zeros((self.num_classes,))

    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)

    def relu_derivative(self, x):
        """ReLU导数"""
        return (x > 0).astype(float)

    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)

    def conv2d(self, input_data, filters, bias, stride=1, padding=1):
        """
        2D卷积操作

        参数:
        input_data -- 输入数据 (batch_size, height, width, channels)
        filters -- 卷积核 (filter_height, filter_width, input_channels, output_channels)
        bias -- 偏置 (output_channels,)
        stride -- 步长
        padding -- 填充

        返回:
        output -- 卷积输出
        """
        batch_size, input_h, input_w, input_c = input_data.shape
        filter_h, filter_w, _, num_filters = filters.shape

        # 添加填充
        if padding > 0:
            input_data = np.pad(input_data,
                              ((0, 0), (padding, padding), (padding, padding), (0, 0)),
                              mode='constant')

        # 计算输出尺寸
        output_h = (input_data.shape[1] - filter_h) // stride + 1
        output_w = (input_data.shape[2] - filter_w) // stride + 1

        # 初始化输出
        output = np.zeros((batch_size, output_h, output_w, num_filters))

        # 执行卷积
        for b in range(batch_size):
            for f in range(num_filters):
                for y in range(output_h):
                    for x in range(output_w):
                        y_start = y * stride
                        y_end = y_start + filter_h
                        x_start = x * stride
                        x_end = x_start + filter_w

                        # 提取输入区域
                        input_region = input_data[b, y_start:y_end, x_start:x_end, :]

                        # 卷积计算
                        output[b, y, x, f] = np.sum(input_region * filters[:, :, :, f]) + bias[f]

        return output

    def max_pool2d(self, input_data, pool_size=2, stride=2):
        """
        最大池化操作

        参数:
        input_data -- 输入数据 (batch_size, height, width, channels)
        pool_size -- 池化窗口大小
        stride -- 步长

        返回:
        output -- 池化输出
        """
        batch_size, input_h, input_w, channels = input_data.shape

        output_h = (input_h - pool_size) // stride + 1
        output_w = (input_w - pool_size) // stride + 1

        output = np.zeros((batch_size, output_h, output_w, channels))

        for b in range(batch_size):
            for c in range(channels):
                for y in range(output_h):
                    for x in range(output_w):
                        y_start = y * stride
                        y_end = y_start + pool_size
                        x_start = x * stride
                        x_end = x_start + pool_size

                        # 最大池化
                        output[b, y, x, c] = np.max(input_data[b, y_start:y_end, x_start:x_end, c])

        return output

    def forward(self, X):
        """
        前向传播

        参数:
        X -- 输入数据 (batch_size, height, width, channels)

        返回:
        output -- 网络输出
        cache -- 缓存的中间结果
        """
        cache = {}

        # 卷积层1 + ReLU + 池化
        conv1_out = self.conv2d(X, self.conv1_filters, self.conv1_bias)
        cache['conv1_out'] = conv1_out

        relu1_out = self.relu(conv1_out)
        cache['relu1_out'] = relu1_out

        pool1_out = self.max_pool2d(relu1_out)
        cache['pool1_out'] = pool1_out

        # 卷积层2 + ReLU + 池化
        conv2_out = self.conv2d(pool1_out, self.conv2_filters, self.conv2_bias)
        cache['conv2_out'] = conv2_out

        relu2_out = self.relu(conv2_out)
        cache['relu2_out'] = relu2_out

        pool2_out = self.max_pool2d(relu2_out)
        cache['pool2_out'] = pool2_out

        # 展平
        batch_size = pool2_out.shape[0]
        flattened = pool2_out.reshape(batch_size, -1)
        cache['flattened'] = flattened

        # 全连接层1 + ReLU
        fc1_out = np.dot(flattened, self.fc1_weights) + self.fc1_bias
        cache['fc1_out'] = fc1_out

        relu_fc1_out = self.relu(fc1_out)
        cache['relu_fc1_out'] = relu_fc1_out

        # 全连接层2 + Softmax
        fc2_out = np.dot(relu_fc1_out, self.fc2_weights) + self.fc2_bias
        cache['fc2_out'] = fc2_out

        output = self.softmax(fc2_out)
        cache['output'] = output

        return output, cache

    def compute_loss(self, y_pred, y_true):
        """
        计算交叉熵损失

        参数:
        y_pred -- 预测概率 (batch_size, num_classes)
        y_true -- 真实标签 (batch_size,)

        返回:
        loss -- 损失值
        """
        batch_size = y_pred.shape[0]

        # 转换为one-hot编码
        y_true_onehot = np.zeros_like(y_pred)
        y_true_onehot[np.arange(batch_size), y_true] = 1

        # 计算交叉熵损失
        epsilon = 1e-15  # 防止log(0)
        y_pred_clipped = np.clip(y_pred, epsilon, 1 - epsilon)
        loss = -np.mean(np.sum(y_true_onehot * np.log(y_pred_clipped), axis=1))

        return loss

    def compute_accuracy(self, y_pred, y_true):
        """
        计算准确率

        参数:
        y_pred -- 预测概率 (batch_size, num_classes)
        y_true -- 真实标签 (batch_size,)

        返回:
        accuracy -- 准确率
        """
        predictions = np.argmax(y_pred, axis=1)
        return np.mean(predictions == y_true)

    def backward(self, X, y_true, cache):
        """
        反向传播

        参数:
        X -- 输入数据
        y_true -- 真实标签
        cache -- 前向传播缓存

        返回:
        gradients -- 梯度字典
        """
        batch_size = X.shape[0]
        gradients = {}

        # 转换为one-hot编码
        y_true_onehot = np.zeros((batch_size, self.num_classes))
        y_true_onehot[np.arange(batch_size), y_true] = 1

        # 输出层梯度
        dfc2_out = cache['output'] - y_true_onehot

        # 全连接层2梯度
        gradients['fc2_weights'] = np.dot(cache['relu_fc1_out'].T, dfc2_out) / batch_size
        gradients['fc2_bias'] = np.mean(dfc2_out, axis=0)

        # 全连接层1梯度
        dfc1_relu = np.dot(dfc2_out, self.fc2_weights.T)
        dfc1_out = dfc1_relu * self.relu_derivative(cache['fc1_out'])

        gradients['fc1_weights'] = np.dot(cache['flattened'].T, dfc1_out) / batch_size
        gradients['fc1_bias'] = np.mean(dfc1_out, axis=0)

        # 展平层梯度
        dflattened = np.dot(dfc1_out, self.fc1_weights.T)
        dpool2_out = dflattened.reshape(cache['pool2_out'].shape)

        # 池化层2梯度（简化实现）
        drelu2_out = self._max_pool_backward(dpool2_out, cache['relu2_out'])

        # ReLU2梯度
        dconv2_out = drelu2_out * self.relu_derivative(cache['conv2_out'])

        # 卷积层2梯度（简化实现）
        gradients['conv2_filters'], gradients['conv2_bias'] = self._conv_backward(
            dconv2_out, cache['pool1_out'], self.conv2_filters)

        return gradients

    def _max_pool_backward(self, dout, input_data):
        """最大池化反向传播（简化实现）"""
        return np.repeat(np.repeat(dout, 2, axis=1), 2, axis=2) / 4

    def _conv_backward(self, dout, input_data, filters):
        """卷积层反向传播（简化实现）"""
        batch_size, out_h, out_w, num_filters = dout.shape

        # 计算滤波器梯度
        dfilters = np.zeros_like(filters)
        dbias = np.mean(dout, axis=(0, 1, 2))

        # 简化的梯度计算
        for f in range(num_filters):
            for b in range(min(batch_size, 10)):  # 限制批次大小以加快计算
                for y in range(min(out_h, 10)):
                    for x in range(min(out_w, 10)):
                        if y < input_data.shape[1] - 2 and x < input_data.shape[2] - 2:
                            input_region = input_data[b, y:y+3, x:x+3, :]
                            dfilters[:, :, :, f] += input_region * dout[b, y, x, f]

        dfilters /= batch_size

        return dfilters, dbias

    def update_parameters(self, gradients):
        """
        更新网络参数

        参数:
        gradients -- 梯度字典
        """
        # 更新全连接层参数
        self.fc2_weights -= self.learning_rate * gradients['fc2_weights']
        self.fc2_bias -= self.learning_rate * gradients['fc2_bias']
        self.fc1_weights -= self.learning_rate * gradients['fc1_weights']
        self.fc1_bias -= self.learning_rate * gradients['fc1_bias']

        # 更新卷积层参数
        if 'conv2_filters' in gradients:
            self.conv2_filters -= self.learning_rate * gradients['conv2_filters']
            self.conv2_bias -= self.learning_rate * gradients['conv2_bias']

    def train(self, X_train, y_train, X_val, y_val, epochs=10, batch_size=32, verbose=True):
        """
        训练网络

        参数:
        X_train -- 训练数据
        y_train -- 训练标签
        X_val -- 验证数据
        y_val -- 验证标签
        epochs -- 训练轮数
        batch_size -- 批次大小
        verbose -- 是否打印训练信息
        """
        n_samples = X_train.shape[0]
        n_batches = n_samples // batch_size

        print(f"开始训练，共 {epochs} 轮，每轮 {n_batches} 个批次")

        for epoch in range(epochs):
            epoch_loss = 0
            epoch_acc = 0

            # 打乱数据
            indices = np.random.permutation(n_samples)
            X_train_shuffled = X_train[indices]
            y_train_shuffled = y_train[indices]

            # 小批次训练
            for batch in range(n_batches):
                start_idx = batch * batch_size
                end_idx = start_idx + batch_size

                X_batch = X_train_shuffled[start_idx:end_idx]
                y_batch = y_train_shuffled[start_idx:end_idx]

                # 前向传播
                y_pred, cache = self.forward(X_batch)

                # 计算损失和准确率
                loss = self.compute_loss(y_pred, y_batch)
                acc = self.compute_accuracy(y_pred, y_batch)

                epoch_loss += loss
                epoch_acc += acc

                # 反向传播
                gradients = self.backward(X_batch, y_batch, cache)

                # 更新参数
                self.update_parameters(gradients)

            # 计算平均损失和准确率
            avg_loss = epoch_loss / n_batches
            avg_acc = epoch_acc / n_batches

            # 验证集评估
            val_pred, _ = self.forward(X_val)
            val_loss = self.compute_loss(val_pred, y_val)
            val_acc = self.compute_accuracy(val_pred, y_val)

            # 记录历史
            self.train_loss_history.append(avg_loss)
            self.train_acc_history.append(avg_acc)
            self.val_loss_history.append(val_loss)
            self.val_acc_history.append(val_acc)

            if verbose:
                print(f"Epoch {epoch+1}/{epochs} - "
                      f"Loss: {avg_loss:.4f} - Acc: {avg_acc:.4f} - "
                      f"Val_Loss: {val_loss:.4f} - Val_Acc: {val_acc:.4f}")

    def predict(self, X):
        """
        预测

        参数:
        X -- 输入数据

        返回:
        predictions -- 预测结果
        """
        y_pred, _ = self.forward(X)
        return np.argmax(y_pred, axis=1)

    def evaluate(self, X_test, y_test):
        """
        评估模型

        参数:
        X_test -- 测试数据
        y_test -- 测试标签

        返回:
        metrics -- 评估指标字典
        """
        predictions = self.predict(X_test)

        # 计算各种指标
        accuracy = accuracy_score(y_test, predictions)

        # 分类报告
        class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer',
                      'dog', 'frog', 'horse', 'ship', 'truck']

        report = classification_report(y_test, predictions,
                                     target_names=class_names,
                                     output_dict=True)

        # 混淆矩阵
        cm = confusion_matrix(y_test, predictions)

        return {
            'accuracy': accuracy,
            'classification_report': report,
            'confusion_matrix': cm,
            'predictions': predictions
        }


def load_cifar10_data():
    """加载CIFAR-10数据集"""
    def unpickle(file):
        with open(file, 'rb') as fo:
            dict = pickle.load(fo, encoding='bytes')
        return dict

    # 检查数据目录
    data_dir = 'cifar-10-python\\cifar-10-batches-py'
    if not os.path.exists(data_dir):
        print("错误：找不到CIFAR-10数据目录")
        print("请确保已解压CIFAR-10数据集到当前目录")
        return None

    # 加载训练数据
    train_data = []
    train_labels = []

    print("正在加载CIFAR-10训练数据...")
    for i in range(1, 6):
        batch_file = os.path.join(data_dir, f'data_batch_{i}')
        if os.path.exists(batch_file):
            batch = unpickle(batch_file)
            train_data.append(batch[b'data'])
            train_labels.extend(batch[b'labels'])
            print(f"  已加载 data_batch_{i}")

    # 加载测试数据
    test_file = os.path.join(data_dir, 'test_batch')
    if os.path.exists(test_file):
        test_batch = unpickle(test_file)
        test_data = test_batch[b'data']
        test_labels = test_batch[b'labels']
        print("  已加载 test_batch")
    else:
        print("错误：找不到测试数据文件")
        return None

    # 加载标签名称
    meta_file = os.path.join(data_dir, 'batches.meta')
    if os.path.exists(meta_file):
        meta = unpickle(meta_file)
        label_names = [name.decode('utf-8') for name in meta[b'label_names']]
        print(f"  标签名称：{label_names}")
    else:
        label_names = None

    # 处理数据
    train_data = np.vstack(train_data)
    train_labels = np.array(train_labels)
    test_data = np.vstack(test_data)
    test_labels = np.array(test_labels)

    # 重塑数据为图像格式 (N, 32, 32, 3),transpose(0, 2, 3, 1) 交换维度顺序(n,32,32,3)
    train_data = train_data.reshape(-1, 3, 32, 32).transpose(0, 2, 3, 1)
    test_data = test_data.reshape(-1, 3, 32, 32).transpose(0, 2, 3, 1)

    # 归一化到[0,1]
    train_data = train_data.astype(np.float32) / 255.0
    test_data = test_data.astype(np.float32) / 255.0

    print(f"\n数据加载完成：")
    print(f"  训练数据形状：{train_data.shape}")
    print(f"  训练标签形状：{train_labels.shape}")
    print(f"  测试数据形状：{test_data.shape}")
    print(f"  测试标签形状：{test_labels.shape}")

    return {
        'train_data': train_data,
        'train_labels': train_labels,
        'test_data': test_data,
        'test_labels': test_labels,
        'label_names': label_names
    }


def plot_training_history(model):
    """绘制训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

    # 损失曲线
    ax1.plot(model.train_loss_history, label='Training Loss')
    ax1.plot(model.val_loss_history, label='Validation Loss')
    ax1.set_title('Model Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    # 准确率曲线
    ax2.plot(model.train_acc_history, label='Training Accuracy')
    ax2.plot(model.val_acc_history, label='Validation Accuracy')
    ax2.set_title('Model Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig('training_history.png', dpi=150, bbox_inches='tight')
    plt.show()


def plot_confusion_matrix(cm, class_names):
    """绘制混淆矩阵"""
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.tight_layout()
    plt.savefig('confusion_matrix.png', dpi=150, bbox_inches='tight')
    plt.show()


def display_sample_predictions(X_test, y_test, predictions, label_names, num_samples=10):
    """显示样本预测结果"""
    fig, axes = plt.subplots(2, 5, figsize=(12, 6))

    for i in range(num_samples):
        row = i // 5
        col = i % 5

        # 显示图像
        axes[row, col].imshow(X_test[i])

        # 设置标题
        true_label = label_names[y_test[i]]
        pred_label = label_names[predictions[i]]
        color = 'green' if y_test[i] == predictions[i] else 'red'

        axes[row, col].set_title(f'True: {true_label}\nPred: {pred_label}',
                                color=color, fontsize=8)
        axes[row, col].axis('off')

    plt.tight_layout()
    plt.savefig('sample_predictions.png', dpi=150, bbox_inches='tight')
    plt.show()


if __name__ == "__main__":
    print("=== CIFAR-10 卷积神经网络分类器 ===")

    # 加载数据
    print("\n1. 加载CIFAR-10数据集...")
    data = load_cifar10_data()

    if data is None:
        print("数据加载失败，程序退出")
        exit(1)

    # 准备数据
    X_train = data['train_data']
    y_train = data['train_labels']
    X_test = data['test_data']
    y_test = data['test_labels']
    label_names = data['label_names']

    # 划分训练集和验证集
    val_split = 0.2
    val_size = int(len(X_train) * val_split)

    # 随机选择验证集
    np.random.seed(42)
    val_indices = np.random.choice(len(X_train), val_size, replace=False)
    train_indices = np.setdiff1d(np.arange(len(X_train)), val_indices)

    X_val = X_train[val_indices]
    y_val = y_train[val_indices]
    X_train = X_train[train_indices]
    y_train = y_train[train_indices]

    print(f"\n数据划分完成：")
    print(f"  训练集：{X_train.shape[0]} 样本")
    print(f"  验证集：{X_val.shape[0]} 样本")
    print(f"  测试集：{X_test.shape[0]} 样本")

    # 创建模型
    print("\n2. 创建卷积神经网络模型...")
    model = ConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.001
    )

    print("模型结构：")
    print("  卷积层1: 32个3x3卷积核 + ReLU + 最大池化")
    print("  卷积层2: 64个3x3卷积核 + ReLU + 最大池化")
    print("  全连接层1: 128个神经元 + ReLU")
    print("  全连接层2: 10个神经元 + Softmax")

    # 训练模型
    print("\n3. 开始训练模型...")
    print("注意：由于是纯NumPy实现，训练可能需要较长时间")

    # 使用较小的数据子集进行快速演示
    subset_size = 1000  # 使用1000个样本进行演示
    X_train_subset = X_train[:subset_size]
    y_train_subset = y_train[:subset_size]
    X_val_subset = X_val[:200]  # 200个验证样本
    y_val_subset = y_val[:200]

    print(f"使用数据子集进行训练（训练：{subset_size}，验证：200）")

    model.train(
        X_train_subset, y_train_subset,
        X_val_subset, y_val_subset,
        epochs=5,  # 较少的轮数用于演示
        batch_size=16,  # 较小的批次大小
        verbose=True
    )

    # 绘制训练历史
    print("\n4. 绘制训练历史...")
    plot_training_history(model)

    # 评估模型
    print("\n5. 评估模型性能...")

    # 在测试集子集上评估
    X_test_subset = X_test[:500]  # 使用500个测试样本
    y_test_subset = y_test[:500]

    metrics = model.evaluate(X_test_subset, y_test_subset)

    print(f"\n=== 模型评估结果 ===")
    print(f"测试准确率: {metrics['accuracy']:.4f}")

    # 打印分类报告
    print("\n分类报告:")
    report = metrics['classification_report']
    for class_name in label_names:
        if class_name in report:
            precision = report[class_name]['precision']
            recall = report[class_name]['recall']
            f1 = report[class_name]['f1-score']
            print(f"  {class_name:12s}: Precision={precision:.3f}, Recall={recall:.3f}, F1={f1:.3f}")

    # 绘制混淆矩阵
    print("\n6. 绘制混淆矩阵...")
    plot_confusion_matrix(metrics['confusion_matrix'], label_names)

    # 显示样本预测结果
    print("\n7. 显示样本预测结果...")
    display_sample_predictions(
        X_test_subset, y_test_subset,
        metrics['predictions'], label_names
    )

    print("\n=== 训练和评估完成 ===")
    print("生成的文件：")
    print("  - training_history.png: 训练历史曲线")
    print("  - confusion_matrix.png: 混淆矩阵")
    print("  - sample_predictions.png: 样本预测结果")

    print("\n注意：")
    print("1. 这是一个简化的CNN实现，用于教学目的")
    print("2. 实际应用中建议使用TensorFlow或PyTorch等框架")
    print("3. 为了演示，只使用了数据子集进行训练")
    print("4. 完整训练需要更多时间和计算资源")
