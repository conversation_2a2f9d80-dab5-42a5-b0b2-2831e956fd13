"""
可自定义CNN快速演示
展示如何自定义网络架构的超参数
"""

import numpy as np
from CustomizableCNN import CustomizableConvolutionalNeuralNetwork

def demo_architecture_customization():
    """演示架构自定义功能"""
    print("="*80)
    print("可自定义CNN架构演示")
    print("="*80)
    
    # 演示1：基础CNN架构
    print("\n【演示1】基础CNN架构")
    print("-" * 50)
    
    model1 = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.001
    )
    
    # 自定义卷积层参数
    model1.add_conv_layer(
        num_filters=16,      # 卷积核数量
        kernel_size=3,       # 卷积核大小
        stride=1,           # 步长
        padding=1,          # 填充
        activation='relu'   # 激活函数
    )
    
    # 自定义池化层参数
    model1.add_pool_layer(
        pool_size=2,        # 池化窗口大小
        stride=2,           # 步长
        pool_type='max'     # 池化类型
    )
    
    model1.add_conv_layer(num_filters=32, kernel_size=3, activation='relu')
    model1.add_pool_layer(pool_size=2, pool_type='max')
    
    # 自定义全连接层参数
    model1.add_fc_layer(
        num_neurons=64,     # 神经元数量
        activation='relu'   # 激活函数
    )
    
    model1.build_model()
    
    # 演示2：深层CNN架构
    print("\n【演示2】深层CNN架构")
    print("-" * 50)
    
    model2 = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.0005
    )
    
    # 第一个卷积块
    model2.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model2.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model2.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    # 第二个卷积块
    model2.add_conv_layer(num_filters=64, kernel_size=3, padding=1, activation='relu')
    model2.add_conv_layer(num_filters=64, kernel_size=3, padding=1, activation='relu')
    model2.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    # 第三个卷积块
    model2.add_conv_layer(num_filters=128, kernel_size=3, padding=1, activation='relu')
    model2.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    # 全连接层
    model2.add_fc_layer(num_neurons=256, activation='relu')
    model2.add_fc_layer(num_neurons=128, activation='relu')
    
    model2.build_model()
    
    # 演示3：使用不同激活函数和池化类型
    print("\n【演示3】多样化激活函数和池化类型")
    print("-" * 50)
    
    model3 = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.001
    )
    
    # 使用不同的激活函数
    model3.add_conv_layer(num_filters=16, kernel_size=5, padding=2, activation='tanh')
    model3.add_pool_layer(pool_size=2, stride=2, pool_type='avg')  # 平均池化
    
    model3.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='sigmoid')
    model3.add_pool_layer(pool_size=2, stride=2, pool_type='max')  # 最大池化
    
    model3.add_conv_layer(num_filters=64, kernel_size=3, padding=1, activation='relu')
    model3.add_pool_layer(pool_size=2, stride=2, pool_type='avg')
    
    # 使用不同激活函数的全连接层
    model3.add_fc_layer(num_neurons=128, activation='tanh')
    model3.add_fc_layer(num_neurons=64, activation='sigmoid')
    model3.add_fc_layer(num_neurons=32, activation='relu')
    
    model3.build_model()
    
    # 演示4：大卷积核架构
    print("\n【演示4】大卷积核架构")
    print("-" * 50)
    
    model4 = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.001
    )
    
    # 使用大卷积核
    model4.add_conv_layer(num_filters=8, kernel_size=7, padding=3, activation='relu')
    model4.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    model4.add_conv_layer(num_filters=16, kernel_size=5, padding=2, activation='relu')
    model4.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    model4.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model4.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    model4.add_fc_layer(num_neurons=64, activation='relu')
    
    model4.build_model()

def demo_parameter_comparison():
    """演示不同参数配置的影响"""
    print("\n" + "="*80)
    print("参数配置对比演示")
    print("="*80)
    
    configs = [
        {
            'name': '小型网络',
            'conv_filters': [8, 16],
            'fc_neurons': [32],
            'kernel_sizes': [3, 3]
        },
        {
            'name': '中型网络',
            'conv_filters': [16, 32, 64],
            'fc_neurons': [128, 64],
            'kernel_sizes': [3, 3, 3]
        },
        {
            'name': '大型网络',
            'conv_filters': [32, 64, 128, 256],
            'fc_neurons': [512, 256, 128],
            'kernel_sizes': [3, 3, 3, 3]
        },
        {
            'name': '大卷积核网络',
            'conv_filters': [16, 32],
            'fc_neurons': [64],
            'kernel_sizes': [7, 5]
        }
    ]
    
    for config in configs:
        print(f"\n【{config['name']}】")
        print("-" * 30)
        
        model = CustomizableConvolutionalNeuralNetwork(
            input_shape=(32, 32, 3),
            num_classes=10,
            learning_rate=0.001
        )
        
        # 添加卷积层
        for i, (filters, kernel_size) in enumerate(zip(config['conv_filters'], config['kernel_sizes'])):
            model.add_conv_layer(
                num_filters=filters,
                kernel_size=kernel_size,
                padding=kernel_size//2,
                activation='relu'
            )
            model.add_pool_layer(pool_size=2, stride=2, pool_type='max')
        
        # 添加全连接层
        for neurons in config['fc_neurons']:
            model.add_fc_layer(num_neurons=neurons, activation='relu')
        
        model.build_model()

def demo_activation_functions():
    """演示不同激活函数的使用"""
    print("\n" + "="*80)
    print("激活函数对比演示")
    print("="*80)
    
    activations = ['relu', 'sigmoid', 'tanh']
    
    for activation in activations:
        print(f"\n【使用 {activation.upper()} 激活函数的网络】")
        print("-" * 40)
        
        model = CustomizableConvolutionalNeuralNetwork(
            input_shape=(32, 32, 3),
            num_classes=10,
            learning_rate=0.001
        )
        
        # 所有层都使用相同的激活函数
        model.add_conv_layer(num_filters=16, kernel_size=3, activation=activation)
        model.add_pool_layer(pool_size=2, pool_type='max')
        
        model.add_conv_layer(num_filters=32, kernel_size=3, activation=activation)
        model.add_pool_layer(pool_size=2, pool_type='max')
        
        model.add_fc_layer(num_neurons=64, activation=activation)
        
        model.build_model()

def demo_pooling_types():
    """演示不同池化类型的使用"""
    print("\n" + "="*80)
    print("池化类型对比演示")
    print("="*80)
    
    pooling_configs = [
        {'name': '全最大池化', 'types': ['max', 'max', 'max']},
        {'name': '全平均池化', 'types': ['avg', 'avg', 'avg']},
        {'name': '混合池化', 'types': ['max', 'avg', 'max']}
    ]
    
    for config in pooling_configs:
        print(f"\n【{config['name']}】")
        print("-" * 30)
        
        model = CustomizableConvolutionalNeuralNetwork(
            input_shape=(32, 32, 3),
            num_classes=10,
            learning_rate=0.001
        )
        
        # 添加卷积和池化层
        filters = [16, 32, 64]
        for i, (filter_num, pool_type) in enumerate(zip(filters, config['types'])):
            model.add_conv_layer(num_filters=filter_num, kernel_size=3, activation='relu')
            model.add_pool_layer(pool_size=2, stride=2, pool_type=pool_type)
        
        model.add_fc_layer(num_neurons=64, activation='relu')
        
        model.build_model()

def print_customization_guide():
    """打印自定义指南"""
    print("\n" + "="*80)
    print("CNN架构自定义指南")
    print("="*80)
    
    print("\n🔧 卷积层自定义参数:")
    print("   • num_filters: 卷积核数量 (8, 16, 32, 64, 128, 256, ...)")
    print("   • kernel_size: 卷积核大小 (1, 3, 5, 7, 9, ...)")
    print("   • stride: 步长 (1, 2, 3, ...)")
    print("   • padding: 填充 (0, 1, 2, ... 或 'same')")
    print("   • activation: 激活函数 ('relu', 'sigmoid', 'tanh', 'linear')")
    
    print("\n🏊 池化层自定义参数:")
    print("   • pool_size: 池化窗口大小 (2, 3, 4, ...)")
    print("   • stride: 步长 (1, 2, 3, ...)")
    print("   • pool_type: 池化类型 ('max', 'avg')")
    
    print("\n🧠 全连接层自定义参数:")
    print("   • num_neurons: 神经元数量 (16, 32, 64, 128, 256, 512, ...)")
    print("   • activation: 激活函数 ('relu', 'sigmoid', 'tanh', 'linear')")
    
    print("\n📊 网络设计建议:")
    print("   • 卷积核数量通常逐层递增: 16 → 32 → 64 → 128")
    print("   • 卷积核大小常用3x3，也可尝试5x5、7x7")
    print("   • 池化通常使用2x2最大池化")
    print("   • 全连接层神经元数量逐层递减")
    print("   • 隐藏层常用ReLU，输出层用Softmax")
    
    print("\n⚡ 性能考虑:")
    print("   • 更多卷积核 = 更强表达能力，但计算量更大")
    print("   • 更大卷积核 = 更大感受野，但参数更多")
    print("   • 更深网络 = 更强表达能力，但训练更困难")
    print("   • 平均池化保留更多信息，最大池化突出显著特征")

if __name__ == "__main__":
    print("🚀 可自定义CNN架构快速演示")
    print("本演示展示如何自定义各种网络架构超参数")
    
    # 运行各种演示
    demo_architecture_customization()
    demo_parameter_comparison()
    demo_activation_functions()
    demo_pooling_types()
    print_customization_guide()
    
    print("\n" + "="*80)
    print("✅ 演示完成！")
    print("="*80)
    
    print("\n📝 总结:")
    print("1. ✅ 可自定义卷积层: 滤波器数量、核大小、步长、填充、激活函数")
    print("2. ✅ 可自定义池化层: 池化大小、步长、池化类型")
    print("3. ✅ 可自定义全连接层: 神经元数量、激活函数")
    print("4. ✅ 支持多种激活函数: ReLU、Sigmoid、Tanh、Linear")
    print("5. ✅ 支持多种池化类型: 最大池化、平均池化")
    print("6. ✅ 自动计算网络参数和输出形状")
    print("7. ✅ 灵活的网络架构设计")
    
    print("\n🎯 使用步骤:")
    print("1. 创建 CustomizableConvolutionalNeuralNetwork 实例")
    print("2. 使用 add_conv_layer() 添加卷积层并设置参数")
    print("3. 使用 add_pool_layer() 添加池化层并设置参数")
    print("4. 使用 add_fc_layer() 添加全连接层并设置参数")
    print("5. 调用 build_model() 构建模型")
    print("6. 使用 train() 训练模型")
    
    print("\n💡 提示:")
    print("• 可以通过修改参数来实验不同的网络架构")
    print("• 建议从简单架构开始，逐步增加复杂度")
    print("• 注意平衡网络复杂度和计算资源")
    print("• 可以混合使用不同的激活函数和池化类型")
