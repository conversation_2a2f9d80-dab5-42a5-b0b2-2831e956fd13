import numpy as np
import matplotlib.pyplot as plt
from PIL import Image


# ================== 核心卷积函数实现 ==================
def conv2d(image, kernel, padding=1, stride=1):
    """
    手动实现二维卷积操作
    :param image: 输入图像矩阵 (H, W)
    :param kernel: 卷积核 (kH, kW)
    :param padding: 填充像素数
    :param stride: 步长
    :return: 卷积结果矩阵
    """
    # 添加零填充
    if padding > 0:
        image = np.pad(image, ((padding, padding), (padding, padding)), mode='constant')

    # 获取输入和卷积核尺寸
    img_h, img_w = image.shape
    kernel_h, kernel_w = kernel.shape

    # 计算输出尺寸
    out_h = (img_h - kernel_h) // stride + 1
    out_w = (img_w - kernel_w) // stride + 1

    # 初始化输出矩阵
    output = np.zeros((out_h, out_w))

    # 滑动窗口进行卷积
    for y in range(0, out_h):
        for x in range(0, out_w):
            # 计算当前窗口位置
            y_start = y * stride
            y_end = y_start + kernel_h
            x_start = x * stride
            x_end = x_start + kernel_w

            # 提取图像局部区域
            img_patch = image[y_start:y_end, x_start:x_end]

            # 执行卷积计算（点乘后求和）
            output[y, x] = np.sum(img_patch * kernel)

    return output


# ================== 图像预处理 ==================
# 1. 读取图像并转换为灰度图
image_path = "dogs.jpg"  # 请替换为实际图片路径
img = Image.open(image_path).convert('L')  # 转为灰度图
img_array = np.array(img) / 255.0  # 归一化到[0,1]

# ================== 定义卷积核示例 ==================
# Sobel水平边缘检测核
sobel_x = np.array([
    [-1, 0, 1],
    [-2, 0, 2],
    [-1, 0, 1]
], dtype=np.float32)

# Sobel垂直边缘检测核
sobel_y = np.array([
    [-1, -2, -1],
    [0, 0, 0],
    [1, 2, 1]
], dtype=np.float32)

# 高斯模糊核（3x3）
gaussian_blur = np.array([
    [1 / 16, 2 / 16, 1 / 16],
    [2 / 16, 4 / 16, 2 / 16],
    [1 / 16, 2 / 16, 1 / 16]
], dtype=np.float32)
#卷积核
kernel_1=np.array([
    [0,1,0],
    [1,-4,1],
    [0,1,0]
],dtype=np.float32)

kernel_2=np.array([
    [0,1,1],
    [-1,0,1],
    [-1,-1,0]
],dtype=np.float32)

kernel_3=np.array([
    [-1,-1,-1],
    [-1,9,-1],
    [-1,-1,-1]
],dtype=np.float32)

kernel_4=np.array([
    [1,1,1],
    [1,-7,1],
    [1,1,1]
],dtype=np.float32)

kernel_5=np.array([
    [0,0,0,0,0],
    [0,0,0,0,0]
    [-1,-1,2,0,0],
    [0,0,0,0,0]
    [0,0,0,0,0]
],dtype=np.float32)

kernel_6=np.array([
    [0,0,1,0,0],
    [0,1,1,1,0]
    [1,1,1,1,1],
    [0,1,1,1,0]
    [0,0,1,0,0]
],dtype=np.float32)




# ================== 执行卷积操作 ==================
# 选择卷积核（这里使用Sobel水平核）
kernel =kernel_1

# 执行卷积
feature_map = conv2d(img_array, kernel, padding=1)

# ================== 结果可视化 ==================
plt.figure(figsize=(12, 6))

# 显示原始图像
plt.subplot(1, 2, 1)
plt.imshow(img_array, cmap='gray')
plt.title('Original Image')
plt.axis('off')

# 显示特征图（取绝对值处理负值）
plt.subplot(1, 2, 2)
plt.imshow(np.abs(feature_map), cmap='gray')
plt.title('Feature Map (Sobel X)')
plt.axis('off')

plt.tight_layout()
plt.show()

"""# ================== 保存特征图 ==================
feature_img = Image.fromarray((np.abs(feature_map) * 255).astype(np.uint8))
feature_img.save('feature_map.jpg')"""