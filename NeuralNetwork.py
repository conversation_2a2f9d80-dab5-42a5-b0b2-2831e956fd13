import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score



class NeuralNetwork:
    def __init__(self, input_size,hidden_size,output_size):
        """
        初始化多项式回归模型
        :param degree: 多项式次数
        """
        # 网络结构参数
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        # 学习参数
        self.learning_rate = 0.01
        self.n_iterations = 5000
        self.tol = 1e-6
        # 初始化权重和偏置
        # 使用He初始化方法，适合ReLU激活函数
        self.W1 = np.random.randn(input_size, hidden_size) * np.sqrt(2.0 / input_size)
        self.b1 = np.zeros((1, hidden_size))
        # 输出层使用Xavier初始化，适合sigmoid激活函数
        self.W2 = np.random.randn(hidden_size, output_size) * np.sqrt(1.0 / hidden_size)
        self.b2 = np.zeros((1, output_size))

        self.X_mean = None
        self.X_std = None
        # 存储训练过程中的损失
        self.loss_history = []



    def _normalize(self, X, fit=False):
        """
        特征标准化 (z-score标准化)
        """
        if fit:
            self.X_mean = np.mean(X, axis=0)
            self.X_std = np.std(X, axis=0)
            # 避免除零
            self.X_std[self.X_std == 0] = 1

        return (X - self.X_mean) / self.X_std
    #输入逻辑函数
    def Sigmodfuntion(self,g_x):
        return 1/(1+np.exp(-np.clip(-g_x, -500, 500)))

    def sigmoid_derivative(self, x):
        """Sigmoid函数的导数"""
        s = self.Sigmodfuntion(x)
        return s * (1 - s)

    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)

    def relu_derivative(self, x):
        """ReLU函数的导数"""
        return (x > 0).astype(float)

    def forward(self, X):
        """
        前向传播
        :param X: 输入数据，形状为(n_samples, input_size)
        :return: 隐藏层输出，输出层输出
        """
        # 第一层：输入层到隐藏层
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = self.relu(self.z1)  # 隐藏层使用ReLU激活

        # 第二层：隐藏层到输出层
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = self.Sigmodfuntion(self.z2)  # 输出层使用Sigmoid激活

        return self.a1, self.a2

    def Lossfuntion(self,y_pred,y):
        """损失函数"""
        epsilon = 1e-15  # 防止log(0)
        y_pred=np.clip(y_pred, epsilon, 1 - epsilon)  # 裁剪避免数值问题
        self.J_loss=-np.mean(y*np.log(y_pred)+(1-y)*np.log(1-y_pred))
        """l2_penalty = (self.alpha / 2) * np.sum(self.weights ** 2) #先去掉正则项"""
        return self.J_loss

    def backward(self, X, y):
        """
        反向传播计算梯度
        :param X: 输入数据，形状为(n_samples, input_size)
        :param y: 真实标签，形状为(n_samples, output_size)
        :return: 各参数的梯度
        """
        n_samples = X.shape[0]
        y = y.reshape(-1, 1)

        # 输出层误差
        dz2 = self.a2 - y
        """print("a2.shape",self.a2.shape,"a2",self.a2)
        print("y.shape",y.shape,"y",y)
        print("dz2.shape",dz2.shape,"dz2",dz2)"""

        # 隐藏层到输出层的梯度
        dW2 = (1 / n_samples) * np.dot(self.a1.T, dz2)
        db2 = (1 / n_samples) * np.sum(dz2, axis=0, keepdims=True)

        # 反向传播到隐藏层
        da1 = np.dot(dz2, self.W2.T)
        dz1 = da1 * self.relu_derivative(self.z1) #矩阵点乘

        # 输入层到隐藏层的梯度
        dW1 = (1 / n_samples) * np.dot(X.T, dz1)
        db1 = (1 / n_samples) * np.sum(dz1, axis=0, keepdims=True)

        return dW1, db1, dW2, db2
    def update_parameters(self, dW1, db1, dW2, db2):
        """
        使用梯度下降更新参数
        """
        self.W1 -= self.learning_rate * dW1
        self.b1 -= self.learning_rate * db1
        self.W2 -= self.learning_rate * dW2
        self.b2 -= self.learning_rate * db2

    def fit(self, X, y):

        # 特征标准化
        X = self._normalize(X, fit=True)
        n_samples, n_features = X.shape

        # 梯度下降
        for i in range(self.n_iterations):
            # 前向传播
            _,y_pred = self.forward(X)
            # 计算损失
            loss = self.Lossfuntion(y_pred,y)
            self.loss_history.append(loss)

            # 检查收敛
            if i > 0 and abs(self.loss_history[-1] - self.loss_history[-2]) < self.tol:
                print(f"训练在 {i} 次迭代后收敛")
                break
            # 反向传播
            dW1, db1, dW2, db2 = self.backward(X, y)
            # 更新参数
            self.update_parameters(dW1, db1, dW2, db2)
        else:
            print(f"达到最大迭代次数 {self.n_iterations}")

    def predict_proba(self, X):
        """
        预测概率
        :param X: 输入数据，形状为(n_samples, input_size)
        :return: 预测概率，形状为(n_samples, output_size)
        """
        _, y_pred = self.forward(X)
        return y_pred

    def predict(self, X, threshold=0.5):
        """预测类别"""
        proba = self.predict_proba(X)
        return (proba >= threshold).astype(int)

    def score(self, X, y):
        """计算准确率"""
        # 确保y的形状正确(二维数组)
        if len(y.shape) == 1:
            y = y.reshape(-1, 1)
        y_pred = self.predict(X)
        return np.mean(y_pred == y)


    # 测试代码
if __name__ == "__main__":
    file = open('Logistic_data_x.txt', 'r')  # 打开txt文件，以只读模式打开
    lines = file.readlines()  # 逐行读取文件内容
    lines = [line.rstrip() for line in lines] #逐行删去换行符

    x_data = []  # 创建一个空的二维数组

    for line in lines:
        row = line.split(' ')  # 将每一行的内容按空格分割成列表
        line.strip("\n")
        x_data.append(row)  # 将每一行的列表添加到二维数组中
    file.close()  # 关闭txt文件
    x_data = np.array(x_data)
    x_data = x_data.astype(np.float64)

    print(x_data.shape)
    print('x_data', x_data)

    # 加载y数据
    y_data = np.loadtxt('Logistic_data_y.txt')
    print('y_data',y_data)
    #划分数据集
    x_train, x_test, y_train, y_test = train_test_split( x_data, y_data, test_size=0.2, random_state=20)

    # 2. 创建并训练模型
    model = NeuralNetwork(input_size=2,hidden_size=10,output_size=1)
    model.fit(x_train, y_train)

    # 3. 预测并评估
    y_pred = model.predict(x_test)
    accuracy = accuracy_score(y_test.reshape(-1, 1), y_pred)
    print(f"测试集准确率: {accuracy:.4f}")



    # 4. 可视化结果
    from pylab import mpl

    # 设置中文显示字体
    mpl.rcParams["font.sans-serif"] = ["SimHei"]
    # 设置正常显示符号
    mpl.rcParams["axes.unicode_minus"] = False
    # 评估模型

    print(f"训试集准确率: {model.score(x_test,y_test):.4f}")


    # 绘制决策边界
    def plot_decision_boundary(model, X, y,x2,y2):
        x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
        y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
        xx, yy = np.meshgrid(np.linspace(x_min, x_max, 100),
                             np.linspace(y_min, y_max, 100))

        Z = model.predict(np.c_[xx.ravel(), yy.ravel()]) #预测每个网格点的类别,
        #ravel() 方法将多维数组展平成一维数组。例如，如果 xx 和 yy 是形状为 (m, n) 的二维数组，那么 xx.ravel() 和 yy.ravel() 会返回长度为 m*n 的一维数组。
        #np.c_[xx.ravel(), yy.ravel()] 将 xx.ravel() 和 yy.ravel() 按列拼接成一个二维数组，其中每一行代表一个网格点的坐标 (x, y)
        Z = Z.reshape(xx.shape)

        plt.figure(figsize=(10, 8))
        plt.contourf(xx, yy, Z, alpha=0.4)
        plt.scatter(X[:, 0], X[:, 1], c=y, s=20, edgecolor='k')
        plt.scatter(x2[:, 0], x2[:, 1], c=y2, s=50, edgecolor='r')
        plt.xlabel('特征1')
        plt.ylabel('特征2')
        plt.title("神经网络决策边界")
        plt.show()


    plot_decision_boundary(model, x_train,y_train,x_test, y_test)
    plot_decision_boundary(model, x_train, y_train, x_test, y_pred)
    # 绘制损失曲线
    plt.figure(figsize=(10, 6))
    plt.plot(model.loss_history)
    plt.xlabel("迭代次数")
    plt.ylabel("损失值")
    plt.title("训练损失曲线")
    plt.grid(True)
    plt.show()
