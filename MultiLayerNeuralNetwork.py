import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.datasets import make_moons
from sklearn.metrics import accuracy_score
from matplotlib.colors import ListedColormap

class MultiLayerNeuralNetwork:
    def __init__(self, layer_dims, activations=None, learning_rate=0.01, n_iterations=5000, 
                 random_seed=None, tol=1e-6):
        """
        初始化多层神经网络
        
        参数:
        layer_dims -- 一个列表，包含每层的神经元数量，例如[2, 4, 3, 1]表示:
                      输入层有2个神经元，第一个隐藏层有4个神经元，
                      第二个隐藏层有3个神经元，输出层有1个神经元
        activations -- 一个列表，包含每层的激活函数，例如['relu', 'relu', 'sigmoid']
                      如果为None，则默认隐藏层使用'relu'，输出层使用'sigmoid'
        learning_rate -- 学习率
        n_iterations -- 最大迭代次数
        random_seed -- 随机种子，用于权重初始化
        tol -- 收敛容差
        """
        self.layer_dims = layer_dims
        self.num_layers = len(layer_dims) - 1  # 不包括输入层
        
        # 设置随机种子
        if random_seed is not None:
            np.random.seed(random_seed)
        
        # 设置默认激活函数
        if activations is None:
            self.activations = ['relu'] * (self.num_layers - 1) + ['sigmoid']
        else:
            assert len(activations) == self.num_layers, "激活函数数量必须等于网络层数"
            self.activations = activations
        
        # 学习参数
        self.learning_rate = learning_rate
        self.n_iterations = n_iterations
        self.tol = tol
        
        # 初始化参数
        self.parameters = self._initialize_parameters()
        
        # 存储训练过程中的损失
        self.loss_history = []
        
        # 数据标准化参数
        self.X_mean = None
        self.X_std = None
    
    def _initialize_parameters(self):
        """
        初始化神经网络的权重和偏置
        
        返回:
        parameters -- 包含权重和偏置的字典
        """
        parameters = {}
        
        for l in range(1, self.num_layers + 1):
            # 根据激活函数选择不同的初始化方法
            if self.activations[l-1] == 'relu':
                # He初始化，适合ReLU激活函数
                parameters['W' + str(l)] = np.random.randn(self.layer_dims[l-1], self.layer_dims[l]) * np.sqrt(2.0 / self.layer_dims[l-1])
            else:
                # Xavier初始化，适合Sigmoid和Tanh激活函数
                parameters['W' + str(l)] = np.random.randn(self.layer_dims[l-1], self.layer_dims[l]) * np.sqrt(1.0 / self.layer_dims[l-1])
            
            parameters['b' + str(l)] = np.zeros((1, self.layer_dims[l]))
        
        return parameters
    
    def _normalize(self, X, fit=False):
        """
        特征标准化 (z-score标准化)
        """
        if fit:
            self.X_mean = np.mean(X, axis=0)
            self.X_std = np.std(X, axis=0)
            # 避免除零
            self.X_std[self.X_std == 0] = 1

        return (X - self.X_mean) / self.X_std
    
    def _activation(self, Z, activation_name):
        """
        计算激活函数
        
        参数:
        Z -- 线性输出
        activation_name -- 激活函数名称: 'sigmoid', 'relu', 'tanh'
        
        返回:
        A -- 激活函数输出
        """
        if activation_name == 'sigmoid':
            return 1 / (1 + np.exp(-np.clip(Z, -500, 500)))  # 裁剪避免溢出
        elif activation_name == 'relu':
            return np.maximum(0, Z)
        elif activation_name == 'tanh':
            return np.tanh(Z)
        else:
            raise ValueError(f"不支持的激活函数: {activation_name}")
    
    def _activation_derivative(self, Z, activation_name):
        """
        计算激活函数的导数
        
        参数:
        Z -- 线性输出
        activation_name -- 激活函数名称: 'sigmoid', 'relu', 'tanh'
        
        返回:
        derivative -- 激活函数的导数
        """
        if activation_name == 'sigmoid':
            A = self._activation(Z, 'sigmoid')
            return A * (1 - A)
        elif activation_name == 'relu':
            return (Z > 0).astype(float)
        elif activation_name == 'tanh':
            #np.power(..., 2)：对tanh(Z)的结果取平方，得到tanh(Z)**2
            return 1 - np.power(np.tanh(Z), 2)
        else:
            raise ValueError(f"不支持的激活函数: {activation_name}")
    
    def forward_propagation(self, X):
        """
        前向传播
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        
        返回:
        caches -- 包含每层的线性输出和激活输出的元组列表
        """
        caches = []
        A = X
        
        # 循环计算每一层
        for l in range(1, self.num_layers + 1):
            A_prev = A
            
            # 线性前向传播
            Z = np.dot(A_prev, self.parameters['W' + str(l)]) + self.parameters['b' + str(l)]
            
            # 激活函数
            A = self._activation(Z, self.activations[l-1])
            
            # 保存当前层的缓存
            cache = (A_prev, Z, A)
            caches.append(cache)
        
        return caches
    
    def compute_loss(self, y_pred, y_true):
        """
        计算均方误差
        
        参数:
        y_pred -- 预测值，形状为(n_samples, output_size)
        y_true -- 真实值，形状为(n_samples, output_size)
        
        返回:
        loss -- 损失值
        """

        # 确保y_true的形状正确
        if len(y_true.shape) == 1:
            y_true = y_true.reshape(-1, 1)
        
        # 均方误差
        #loss = -np.mean(y_true * np.log(y_pred) + (1 - y_true) * np.log(1 - y_pred))
        mse = np.mean((y_pred - y_true) ** 2)
        return mse
    
    def backward_propagation(self, X, y, caches):
        """
        反向传播计算梯度
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        y -- 真实标签，形状为(n_samples,)
        caches -- 前向传播的缓存
        
        返回:
        gradients -- 包含每层权重和偏置梯度的字典
        """
        n_samples = X.shape[0]
        gradients = {}
        
        # 确保y的形状正确
        if len(y.shape) == 1:
            y = y.reshape(-1, 1)
        
        # 输出层的误差
        A_final = caches[-1][2]  # 最后一层的激活输出
        dZ = A_final - y
        
        # 循环计算每一层的梯度，从后向前
        for l in reversed(range(1, self.num_layers + 1)):
            # 获取当前层的缓存
            A_prev, Z, A = caches[l-1]
            # 确保使用NumPy数组，只要当前pandas里面数组的值
            if hasattr(A_prev, 'values'):
                A_prev = A_prev.values
            if hasattr(dZ, 'values'):
                dZ = dZ.values
            
            # 计算当前层的权重和偏置梯度
            gradients['dW' + str(l)] = (1/n_samples) * np.dot(A_prev.T, dZ)
            gradients['db' + str(l)] = (1/n_samples) * np.sum(dZ, axis=0, keepdims=True)
            
            # 如果不是第一层，计算前一层的误差
            if l > 1:
                dA_prev = np.dot(dZ, self.parameters['W' + str(l)].T)
                dZ = dA_prev * self._activation_derivative(caches[l-2][1], self.activations[l-2])
        
        return gradients
    
    def update_parameters(self, gradients):
        """
        使用梯度下降更新参数
        
        参数:
        gradients -- 包含每层权重和偏置梯度的字典
        """
        # 梯度裁剪，防止梯度爆炸
        clip_value = 5.0
        
        for l in range(1, self.num_layers + 1):
            # 裁剪梯度
            dW = np.clip(gradients['dW' + str(l)], -clip_value, clip_value)
            db = np.clip(gradients['db' + str(l)], -clip_value, clip_value)
            
            # 更新参数
            self.parameters['W' + str(l)] -= self.learning_rate * dW
            self.parameters['b' + str(l)] -= self.learning_rate * db
    
    def fit(self, X, y, verbose=True):
        """
        训练神经网络
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        y -- 真实标签，形状为(n_samples,)
        verbose -- 是否打印训练进度
        """
        # 特征标准化
        X = self._normalize(X, fit=True)
        
        # 训练循环
        for i in range(self.n_iterations):
            # 前向传播
            caches = self.forward_propagation(X)
            
            # 获取最后一层的输出
            y_pred = caches[-1][2]
            
            # 计算损失
            loss = self.compute_loss(y_pred, y)
            self.loss_history.append(loss)
            
            # 打印训练进度
            if verbose and i % 1000 == 0:
                print(f"迭代 {i}, 损失: {loss:.6f}")
            
            # 检查收敛
            if i > 0 and abs(self.loss_history[-1] - self.loss_history[-2]) < self.tol:
                if verbose:
                    print(f"训练在 {i} 次迭代后收敛")
                break
            
            # 反向传播
            gradients = self.backward_propagation(X, y, caches)
            
            # 更新参数
            self.update_parameters(gradients)
        else:
            if verbose:
                print(f"达到最大迭代次数 {self.n_iterations}")
    
    def predict_proba(self, X):
        """
        预测概率
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        
        返回:
        y_pred -- 预测概率，形状为(n_samples, output_size)
        """
        # 标准化输入数据
        if self.X_mean is not None and self.X_std is not None:
            X = (X - self.X_mean) / self.X_std
        
        # 前向传播
        caches = self.forward_propagation(X)
        
        # 返回最后一层的输出
        return caches[-1][2]
    
    def predict(self, X, threshold=0.5):
        """
        预测类别
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        threshold -- 分类阈值
        
        返回:
        y_pred -- 预测类别，形状为(n_samples, output_size)
        """
        proba = self.predict_proba(X)
        return (proba >= threshold).astype(int)
    
    def score(self, X, y):
        """
        计算准确率
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        y -- 真实标签，形状为(n_samples,)
        
        返回:
        accuracy -- 准确率
        """
        # 确保y的形状正确
        if len(y.shape) == 1:
            y = y.reshape(-1, 1)
            
        y_pred = self.predict(X)
        return np.mean(y_pred == y)
    
    """
    def plot_decision_boundary(self, X, y, title="决策边界"):
        
        绘制决策边界
        
        参数:
        X -- 输入特征，形状为(n_samples, 2)
        y -- 真实标签，形状为(n_samples,)
        title -- 图表标题"""

    """""# 确保X只有两个特征
        assert X.shape[1] == 2, "绘制决策边界需要恰好两个特征"
        
        # 创建网格
        h = 0.01
        x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
        y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                             np.arange(y_min, y_max, h))
        
        # 对网格点进行标准化
        grid_points = np.c_[xx.ravel(), yy.ravel()]
        if self.X_mean is not None and self.X_std is not None:
            grid_points = (grid_points - self.X_mean) / self.X_std
        
        # 预测网格点的类别
        Z = self.predict(grid_points)
        Z = Z.reshape(xx.shape)
        
        # 绘制决策边界和散点图
        plt.figure(figsize=(10, 8))
        
        # 使用黄色和紫色的自定义颜色映射
        custom_cmap = ListedColormap(['#9370DB', '#FFD700'])  # 紫色和黄色
        
        plt.contourf(xx, yy, Z, alpha=0.8, cmap=custom_cmap, levels=[-0.5, 0.5, 1.5])
        plt.scatter(X[:, 0], X[:, 1], c=y, cmap=custom_cmap, s=20, edgecolor='k')
        
        plt.xlabel('特征1')
        plt.ylabel('特征2')
        plt.title(title)
        plt.colorbar()
        plt.show()"""


    
    def plot_loss_history(self):
        """绘制损失曲线"""
        plt.figure(figsize=(10, 6))
        plt.plot(self.loss_history)
        plt.xlabel("迭代次数")
        plt.ylabel("损失值")
        plt.title("训练损失曲线")
        plt.grid(True)
        plt.show()



# 测试代码
if __name__ == "__main__":
    # 生成月牙形数据集
    X, y = make_moons(n_samples=1000, noise=0.2, random_state=42)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # 创建并训练神经网络
    # 定义网络结构: [输入层大小, 隐藏层1大小, 隐藏层2大小, ..., 输出层大小]
    layer_dims = [2, 10, 5, 1]  # 2个输入特征, 两个隐藏层(10和5个神经元), 1个输出
    
    # 定义每层的激活函数
    activations = ['relu', 'relu', 'sigmoid']  # 两个隐藏层使用ReLU, 输出层使用Sigmoid
    
    # 创建模型
    model = MultiLayerNeuralNetwork(
        layer_dims=layer_dims,
        activations=activations,
        learning_rate=0.01,
        n_iterations=5000,
        random_seed=42
    )
    
    # 训练模型
    model.fit(X_train, y_train)
    
    # 评估模型
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test.reshape(-1, 1), y_pred)
    print(f"测试集准确率: {accuracy:.4f}")
    
    # 可视化决策边界
    model.plot_decision_boundary(X, y, f"{len(layer_dims)-1}层神经网络决策边界")
    
    # 绘制损失曲线
    model.plot_loss_history()
