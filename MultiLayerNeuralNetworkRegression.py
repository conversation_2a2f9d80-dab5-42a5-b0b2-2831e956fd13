import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.datasets import make_regression
import pandas as pd
from matplotlib.colors import ListedColormap

class MultiLayerNeuralNetwork:
    def __init__(self, layer_dims, activations=None, learning_rate=0.01, n_iterations=5000, 
                 random_seed=None, tol=1e-6):
        """
        初始化多层神经网络
        
        参数:
        layer_dims -- 一个列表，包含每层的神经元数量，例如[2, 4, 3, 1]表示:
                      输入层有2个神经元，第一个隐藏层有4个神经元，
                      第二个隐藏层有3个神经元，输出层有1个神经元
        activations -- 一个列表，包含每层的激活函数，例如['relu', 'relu', 'linear']
                      如果为None，则默认隐藏层使用'relu'，输出层使用'linear'
        learning_rate -- 学习率
        n_iterations -- 最大迭代次数
        random_seed -- 随机种子，用于权重初始化
        tol -- 收敛容差
        """
        self.layer_dims = layer_dims
        self.num_layers = len(layer_dims) - 1  # 不包括输入层
        
        # 设置随机种子
        if random_seed is not None:
            np.random.seed(random_seed)
        
        # 设置默认激活函数
        if activations is None:
            # 对于回归问题，输出层使用线性激活函数
            self.activations = ['relu'] * (self.num_layers - 1) + ['linear']
        else:
            assert len(activations) == self.num_layers, "激活函数数量必须等于网络层数"
            self.activations = activations
        
        # 学习参数
        self.learning_rate = learning_rate
        self.n_iterations = n_iterations
        self.tol = tol
        
        # 初始化参数
        self.parameters = self._initialize_parameters()
        
        # 存储训练过程中的损失
        self.loss_history = []
        
        # 数据标准化参数
        self.X_mean = None
        self.X_std = None
        self.y_mean = None
        self.y_std = None
    
    def _initialize_parameters(self):
        """
        初始化神经网络的权重和偏置
        
        返回:
        parameters -- 包含权重和偏置的字典
        """
        parameters = {}
        
        for l in range(1, self.num_layers + 1):
            # 根据激活函数选择不同的初始化方法
            if self.activations[l-1] == 'relu':
                # He初始化，适合ReLU激活函数
                parameters['W' + str(l)] = np.random.randn(self.layer_dims[l-1], self.layer_dims[l]) * np.sqrt(2.0 / self.layer_dims[l-1])
            else:
                # Xavier初始化，适合Sigmoid和Tanh激活函数
                parameters['W' + str(l)] = np.random.randn(self.layer_dims[l-1], self.layer_dims[l]) * np.sqrt(1.0 / self.layer_dims[l-1])
            
            parameters['b' + str(l)] = np.zeros((1, self.layer_dims[l]))
        
        return parameters
    
    def _normalize_X(self, X, fit=False):
        """
        特征标准化 (z-score标准化)
        """
        if fit:
            self.X_mean = np.mean(X, axis=0)
            self.X_std = np.std(X, axis=0)
            # 避免除零
            self.X_std[self.X_std == 0] = 1

        return (X - self.X_mean) / self.X_std
    
    def _normalize_y(self, y, fit=False):
        """
        目标值标准化 (z-score标准化)
        """
        if fit:
            self.y_mean = np.mean(y, axis=0)
            self.y_std = np.std(y, axis=0)
            # 避免除零
            if np.any(self.y_std == 0):
                self.y_std[self.y_std == 0] = 1

        return (y - self.y_mean) / self.y_std
    
    def _denormalize_y(self, y):
        """
        目标值反标准化
        """
        if self.y_mean is not None and self.y_std is not None:
            return y * self.y_std + self.y_mean
        return y
    
    def _activation(self, Z, activation_name):
        """
        计算激活函数
        
        参数:
        Z -- 线性输出
        activation_name -- 激活函数名称: 'sigmoid', 'relu', 'tanh', 'linear'
        
        返回:
        A -- 激活函数输出
        """
        if activation_name == 'sigmoid':
            return 1 / (1 + np.exp(-np.clip(Z, -500, 500)))  # 裁剪避免溢出
        elif activation_name == 'relu':
            return np.maximum(0, Z)
        elif activation_name == 'tanh':
            return np.tanh(Z)
        elif activation_name == 'linear':
            return Z  # 线性激活函数直接返回输入
        else:
            raise ValueError(f"不支持的激活函数: {activation_name}")
    
    def _activation_derivative(self, Z, activation_name):
        """
        计算激活函数的导数
        
        参数:
        Z -- 线性输出
        activation_name -- 激活函数名称: 'sigmoid', 'relu', 'tanh', 'linear'
        
        返回:
        derivative -- 激活函数的导数
        """
        if activation_name == 'sigmoid':
            A = self._activation(Z, 'sigmoid')
            return A * (1 - A)
        elif activation_name == 'relu':
            return (Z > 0).astype(float)
        elif activation_name == 'tanh':
            return 1 - np.power(np.tanh(Z), 2)
        elif activation_name == 'linear':
            # 线性函数的导数是1
            return np.ones_like(Z)
        else:
            raise ValueError(f"不支持的激活函数: {activation_name}")
    
    def forward_propagation(self, X):
        """
        前向传播
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        
        返回:
        caches -- 包含每层的线性输出和激活输出的元组列表
        """
        caches = []
        A = X
        
        # 循环计算每一层
        for l in range(1, self.num_layers + 1):
            A_prev = A
            
            # 线性前向传播 (m,n)*(n,o)
            Z = np.dot(A_prev, self.parameters['W' + str(l)]) + self.parameters['b' + str(l)]
            
            # 激活函数
            A = self._activation(Z, self.activations[l-1])
            
            # 保存当前层的缓存
            cache = (A_prev, Z, A)
            caches.append(cache)
        
        return caches
    
    def compute_loss(self, y_pred, y_true):
        """
        计算均方误差
        
        参数:
        y_pred -- 预测值，形状为(n_samples, output_size)
        y_true -- 真实值，形状为(n_samples, output_size)
        
        返回:
        loss -- 损失值
        """
        # 确保y_true的形状正确
        if len(y_true.shape) == 1:
            y_true = y_true.reshape(-1, 1)
        
        # 均方误差 (MSE)
        mse = np.mean((y_pred - y_true) ** 2)
        return mse
    
    def backward_propagation(self, X, y, caches):
        """
        反向传播计算梯度
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        y -- 真实标签，形状为(n_samples,)
        caches -- 前向传播的缓存
        
        返回:
        gradients -- 包含每层权重和偏置梯度的字典
        """
        n_samples = X.shape[0]
        gradients = {}
        
        # 确保y的形状正确
        if len(y.shape) == 1:
            y = y.reshape(-1, 1)
        
        # 输出层的误差
        A_final = caches[-1][2]  # 最后一层的激活输出
        Z_final = caches[-1][1]  # 最后一层的线性输出
        
        # 对于均方误差损失，输出层的误差是 2*(A_final - y) * f'(Z_final)
        # 其中f'(Z_final)是输出层激活函数的导数
        dA = 2 * (A_final - y) / n_samples  # 2/n 是均方误差对预测值的导数
        dZ = dA * self._activation_derivative(Z_final, self.activations[-1]) #点乘
        
        # 循环计算每一层的梯度，从后向前
        for l in reversed(range(1, self.num_layers + 1)):
            # 获取当前层的缓存
            A_prev, Z, A = caches[l-1]
            # 确保使用NumPy数组，只要当前pandas里面数组的值
            if hasattr(A_prev, 'values'):
                A_prev = A_prev.values
            if hasattr(dZ, 'values'):
                dZ = dZ.values
            
            # 计算当前层的权重和偏置梯度
            gradients['dW' + str(l)] = np.dot(A_prev.T, dZ)
            gradients['db' + str(l)] = np.sum(dZ, axis=0, keepdims=True)
            
            # 如果不是第一层，计算前一层的误差
            if l > 1:
                dA_prev = np.dot(dZ, self.parameters['W' + str(l)].T)
                dZ = dA_prev * self._activation_derivative(caches[l-2][1], self.activations[l-2])
        
        return gradients
    
    def update_parameters(self, gradients):
        """
        使用梯度下降更新参数
        
        参数:
        gradients -- 包含每层权重和偏置梯度的字典
        """
        # 梯度裁剪，防止梯度爆炸
        clip_value = 5.0
        
        for l in range(1, self.num_layers + 1):
            # 裁剪梯度
            dW = np.clip(gradients['dW' + str(l)], -clip_value, clip_value)
            db = np.clip(gradients['db' + str(l)], -clip_value, clip_value)
            
            # 更新参数
            self.parameters['W' + str(l)] -= self.learning_rate * dW
            self.parameters['b' + str(l)] -= self.learning_rate * db
    
    def fit(self, X, y, verbose=True):
        """
        训练神经网络
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        y -- 真实标签，形状为(n_samples,)
        verbose -- 是否打印训练进度
        """
        # 特征标准化
        X = self._normalize_X(X, fit=True)
        
        # 目标值标准化（对回归问题很重要）
        if len(y.shape) == 1:
            y = y.reshape(-1, 1)
        y = self._normalize_y(y, fit=True)
        
        # 训练循环
        for i in range(self.n_iterations):
            # 前向传播
            caches = self.forward_propagation(X)
            
            # 获取最后一层的输出
            y_pred = caches[-1][2]
            
            # 计算损失
            loss = self.compute_loss(y_pred, y)
            self.loss_history.append(loss)
            
            # 打印训练进度
            if verbose and i % 1000 == 0:
                print(f"迭代 {i}, 损失: {loss:.6f}")
            
            # 检查收敛
            if i > 0 and abs(self.loss_history[-1] - self.loss_history[-2]) < self.tol:
                if verbose:
                    print(f"训练在 {i} 次迭代后收敛")
                break
            
            # 反向传播
            gradients = self.backward_propagation(X, y, caches)
            
            # 更新参数
            self.update_parameters(gradients)
        else:
            if verbose:
                print(f"达到最大迭代次数 {self.n_iterations}")
    
    def predict(self, X):
        """
        预测值
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        
        返回:
        y_pred -- 预测值，形状为(n_samples, output_size)
        """
        # 标准化输入数据
        if self.X_mean is not None and self.X_std is not None:
            X = (X - self.X_mean) / self.X_std
        
        # 前向传播
        caches = self.forward_propagation(X)
        
        # 获取预测值
        y_pred = caches[-1][2]
        
        # 反标准化预测值
        return self._denormalize_y(y_pred)
    
    def score(self, X, y):
        """
        计算R方分数（决定系数）
        
        参数:
        X -- 输入数据，形状为(n_samples, n_features)
        y -- 真实值，形状为(n_samples,)
        
        返回:
        r2_score -- R方分数，范围为[-∞, 1]，1表示完美预测
        """
        # 确保y的形状正确
        if len(y.shape) == 1:
            y = y.reshape(-1, 1)
            
        y_pred = self.predict(X)
        
        # 计算R方分数
        ss_tot = np.sum((y - np.mean(y)) ** 2)  # 总平方和
        ss_res = np.sum((y - y_pred) ** 2)      # 残差平方和
        
        if ss_tot == 0:  # 避免除零
            return 0
        else:
            return 1 - (ss_res / ss_tot)  # R方分数公式
    
    def plot_loss_history(self):
        """绘制损失曲线"""
        plt.figure(figsize=(10, 6))
        plt.plot(self.loss_history)
        plt.xlabel("迭代次数")
        plt.ylabel("损失值")
        plt.title("训练损失曲线")
        plt.grid(True)
        plt.show()

    
    def plot_prediction(self, X, y, title="回归预测结果"):
        """
        绘制回归预测结果
        
        参数:
        X -- 输入特征，形状为(n_samples, n_features)
        y -- 真实值，形状为(n_samples,)
        title -- 图表标题
        """
        # 确保y的形状正确
        if len(y.shape) == 1:
            y = y.reshape(-1, 1)
        
        # 预测值
        y_pred = self.predict(X)
        
        # 如果是一维特征，绘制散点图
        if X.shape[1] == 1:
            plt.figure(figsize=(10, 6))
            plt.scatter(X, y, color='blue', label='实际值')
            
            # 按X排序，以便绘制平滑的预测线
            sort_idx = np.argsort(X.ravel())
            plt.plot(X[sort_idx], y_pred[sort_idx], color='red', linewidth=2, label='预测值')
            
            plt.xlabel('特征')
            plt.ylabel('目标值')
            plt.title(title)
            plt.legend()
            plt.grid(True)
            plt.show()
        
        # 如果是二维特征，绘制3D图
        elif X.shape[1] == 2:
            from mpl_toolkits.mplot3d import Axes3D
            
            fig = plt.figure(figsize=(12, 8))
            ax = fig.add_subplot(111, projection='3d')
            
            # 绘制实际值
            ax.scatter(X[:, 0], X[:, 1], y, color='blue', label='实际值')
            
            # 绘制预测值
            ax.scatter(X[:, 0], X[:, 1], y_pred, color='red', label='预测值')
            
            ax.set_xlabel('特征1')
            ax.set_ylabel('特征2')
            ax.set_zlabel('目标值')
            ax.set_title(title)
            ax.legend()
            plt.show()
        
        # 如果特征维度大于2，绘制预测值与实际值的对比图
        else:
            plt.figure(figsize=(10, 6))
            plt.scatter(y, y_pred)
            
            # 绘制理想的预测线（y=x）
            min_val = min(np.min(y), np.min(y_pred))
            max_val = max(np.max(y), np.max(y_pred))
            plt.plot([min_val, max_val], [min_val, max_val], 'r--')
            
            plt.xlabel('实际值')
            plt.ylabel('预测值')
            plt.title(f"{title} - 预测值与实际值对比")
            plt.grid(True)
            plt.axis('equal')
            plt.show()


# 测试代码
if __name__ == "__main__":
    def process_excel_data(file_path):
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 显示原始数据的前几行
        print("原始数据的前5行:")
        print(df.head())

        # 删除第一列
        df = df.iloc[:, 1:]

        # 显示删除第一列后的数据
        print("\n删除第一列后的数据前5行:")
        print(df.head())

        # 获取特征标签（假设第一行是特征标签）
        feature_labels = df.iloc[0, :].tolist()

        # 从第二行开始获取实际数据
        data = df.iloc[1:, :].reset_index(drop=True)

        # 分离特征和目标变量
        # y_data为原始数据的第2列和第3列（删除第一列后的索引0和1）
        y_data = data.iloc[:, [0, 1]]

        # x_data为剩余的列
        x_data = data.iloc[:, 2:]

        print("\n特征标签:")
        print(feature_labels)

        print("\ny_data的前5行:")
        print(y_data.head())

        print("\nx_data的前5行:")
        print(x_data.head())

        print("\nx_data形状:", x_data.shape)
        print("y_data形状:", y_data.shape)

        return x_data, y_data, feature_labels
    # 生成回归数据集
    x_data, y_data, feature_labels = process_excel_data('NN_data.xlsx')
    
    # 划分训练集和测试集
    X_train, X_temp, y_train, y_temp = train_test_split(x_data, y_data, test_size=0.2, random_state=42)
    X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42)
    
    # 创建并训练神经网络
    # 定义网络结构: [输入层大小, 隐藏层1大小, 隐藏层2大小, ..., 输出层大小]
    layer_dims = [8, 20, 10, 2]  # 1个输入特征, 两个隐藏层(20和10个神经元), 1个输出
    
    # 定义每层的激活函数
    activations = ['relu', 'relu', 'linear']  # 两个隐藏层使用ReLU, 输出层使用线性激活函数
    
    # 创建模型
    model = MultiLayerNeuralNetwork(
        layer_dims=layer_dims,
        activations=activations,
        learning_rate=0.01,
        n_iterations=10000,
        random_seed=42
    )

    
    # 训练模型
    model.fit(X_train, y_train)
    
    # 评估模型
    r2_score = model.score(X_test, y_test)
    print(f"测试集R方分数: {r2_score:.4f}")
    from pylab import mpl

    # 设置中文显示字体
    mpl.rcParams["font.sans-serif"] = ["SimHei"]
    # 设置正常显示符号
    mpl.rcParams["axes.unicode_minus"] = False
    # 评估模型

    print(f"训试集准确率: {model.score(X_test, y_test):.4f}")
    
    # 可视化预测结果
    model.plot_prediction(X_val, y_val, f"{len(layer_dims)-1}层神经网络回归结果")
    
    # 绘制损失曲线
    model.plot_loss_history()
