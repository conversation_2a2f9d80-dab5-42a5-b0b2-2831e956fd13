"""
可自定义CNN训练示例
展示如何使用CustomizableCNN构建和训练不同架构的神经网络
"""

import numpy as np
import matplotlib.pyplot as plt
from CustomizableCNN import CustomizableConvolutionalNeuralNetwork, load_cifar10_data, plot_training_history

def create_sample_data(num_samples=1000):
    """创建示例数据用于快速测试"""
    np.random.seed(42)
    
    # 生成随机图像数据
    X = np.random.rand(num_samples, 32, 32, 3).astype(np.float32)
    
    # 生成随机标签
    y = np.random.randint(0, 10, num_samples)
    
    return X, y

def example_1_simple_cnn():
    """示例1：简单CNN架构"""
    print("="*60)
    print("示例1：训练简单CNN架构")
    print("="*60)
    
    # 创建示例数据
    X_train, y_train = create_sample_data(800)
    X_val, y_val = create_sample_data(200)
    X_test, y_test = create_sample_data(200)
    
    # 构建模型
    model = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.01
    )
    
    # 添加网络层
    model.add_conv_layer(num_filters=8, kernel_size=3, padding=1, activation='relu')
    model.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    model.add_conv_layer(num_filters=16, kernel_size=3, padding=1, activation='relu')
    model.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    model.add_fc_layer(num_neurons=32, activation='relu')
    
    # 构建模型
    model.build_model()
    
    # 训练模型
    print("\n开始训练...")
    model.train(X_train, y_train, X_val, y_val, epochs=3, batch_size=16, verbose=True)
    
    # 评估模型
    print("\n评估模型...")
    metrics = model.evaluate(X_test, y_test)
    print(f"测试准确率: {metrics['accuracy']:.4f}")
    
    # 绘制训练历史
    plot_training_history(model)
    
    return model

def example_2_deep_cnn():
    """示例2：深层CNN架构"""
    print("="*60)
    print("示例2：训练深层CNN架构")
    print("="*60)
    
    # 创建示例数据
    X_train, y_train = create_sample_data(600)
    X_val, y_val = create_sample_data(150)
    X_test, y_test = create_sample_data(150)
    
    # 构建模型
    model = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.005
    )
    
    # 添加更多层
    model.add_conv_layer(num_filters=16, kernel_size=3, padding=1, activation='relu')
    model.add_conv_layer(num_filters=16, kernel_size=3, padding=1, activation='relu')
    model.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    model.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    
    model.add_fc_layer(num_neurons=64, activation='relu')
    model.add_fc_layer(num_neurons=32, activation='relu')
    
    # 构建模型
    model.build_model()
    
    # 训练模型
    print("\n开始训练...")
    model.train(X_train, y_train, X_val, y_val, epochs=3, batch_size=16, verbose=True)
    
    # 评估模型
    print("\n评估模型...")
    metrics = model.evaluate(X_test, y_test)
    print(f"测试准确率: {metrics['accuracy']:.4f}")
    
    return model

def example_3_different_activations():
    """示例3：使用不同激活函数的CNN"""
    print("="*60)
    print("示例3：使用不同激活函数的CNN")
    print("="*60)
    
    # 创建示例数据
    X_train, y_train = create_sample_data(500)
    X_val, y_val = create_sample_data(100)
    X_test, y_test = create_sample_data(100)
    
    # 构建模型
    model = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.01
    )
    
    # 使用不同的激活函数
    model.add_conv_layer(num_filters=8, kernel_size=5, padding=2, activation='tanh')
    model.add_pool_layer(pool_size=2, stride=2, pool_type='avg')
    model.add_conv_layer(num_filters=16, kernel_size=3, padding=1, activation='sigmoid')
    model.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    model.add_fc_layer(num_neurons=50, activation='tanh')
    model.add_fc_layer(num_neurons=25, activation='relu')
    
    # 构建模型
    model.build_model()
    
    # 训练模型
    print("\n开始训练...")
    model.train(X_train, y_train, X_val, y_val, epochs=3, batch_size=16, verbose=True)
    
    # 评估模型
    print("\n评估模型...")
    metrics = model.evaluate(X_test, y_test)
    print(f"测试准确率: {metrics['accuracy']:.4f}")
    
    return model

def compare_architectures():
    """比较不同架构的性能"""
    print("="*60)
    print("架构性能比较")
    print("="*60)
    
    # 创建相同的测试数据
    X_test, y_test = create_sample_data(200)
    
    architectures = [
        {
            'name': '浅层CNN',
            'config': [
                ('conv', {'num_filters': 8, 'kernel_size': 3, 'activation': 'relu'}),
                ('pool', {'pool_size': 2, 'pool_type': 'max'}),
                ('fc', {'num_neurons': 32, 'activation': 'relu'})
            ]
        },
        {
            'name': '中等CNN',
            'config': [
                ('conv', {'num_filters': 16, 'kernel_size': 3, 'activation': 'relu'}),
                ('pool', {'pool_size': 2, 'pool_type': 'max'}),
                ('conv', {'num_filters': 32, 'kernel_size': 3, 'activation': 'relu'}),
                ('pool', {'pool_size': 2, 'pool_type': 'max'}),
                ('fc', {'num_neurons': 64, 'activation': 'relu'})
            ]
        },
        {
            'name': '深层CNN',
            'config': [
                ('conv', {'num_filters': 16, 'kernel_size': 3, 'activation': 'relu'}),
                ('conv', {'num_filters': 16, 'kernel_size': 3, 'activation': 'relu'}),
                ('pool', {'pool_size': 2, 'pool_type': 'max'}),
                ('conv', {'num_filters': 32, 'kernel_size': 3, 'activation': 'relu'}),
                ('pool', {'pool_size': 2, 'pool_type': 'max'}),
                ('fc', {'num_neurons': 64, 'activation': 'relu'}),
                ('fc', {'num_neurons': 32, 'activation': 'relu'})
            ]
        }
    ]
    
    results = []
    
    for arch in architectures:
        print(f"\n训练 {arch['name']}...")
        
        # 创建训练数据
        X_train, y_train = create_sample_data(400)
        X_val, y_val = create_sample_data(100)
        
        # 构建模型
        model = CustomizableConvolutionalNeuralNetwork(
            input_shape=(32, 32, 3),
            num_classes=10,
            learning_rate=0.01
        )
        
        # 添加层
        for layer_type, params in arch['config']:
            if layer_type == 'conv':
                model.add_conv_layer(**params)
            elif layer_type == 'pool':
                model.add_pool_layer(**params)
            elif layer_type == 'fc':
                model.add_fc_layer(**params)
        
        # 构建和训练
        model.build_model()
        model.train(X_train, y_train, X_val, y_val, epochs=2, batch_size=16, verbose=False)
        
        # 评估
        metrics = model.evaluate(X_test, y_test)
        results.append({
            'name': arch['name'],
            'accuracy': metrics['accuracy'],
            'params': sum(p.size for p in model.parameters.values())
        })
        
        print(f"{arch['name']} - 准确率: {metrics['accuracy']:.4f}, 参数数量: {results[-1]['params']:,}")
    
    # 显示比较结果
    print("\n" + "="*60)
    print("架构比较结果:")
    print("="*60)
    for result in results:
        print(f"{result['name']:12s} - 准确率: {result['accuracy']:.4f}, 参数: {result['params']:,}")

def custom_architecture_builder():
    """交互式架构构建器"""
    print("="*60)
    print("自定义架构构建器")
    print("="*60)
    print("这个示例展示了如何灵活地构建自定义CNN架构")
    
    # 用户可以修改这些参数来创建不同的架构
    custom_configs = [
        {
            'name': '自定义架构1',
            'layers': [
                ('conv', 12, 5, 'relu'),      # (类型, 滤波器数, 核大小, 激活函数)
                ('pool', 2, 'max'),           # (类型, 池化大小, 池化类型)
                ('conv', 24, 3, 'relu'),
                ('pool', 2, 'avg'),
                ('fc', 48, 'tanh'),           # (类型, 神经元数, 激活函数)
                ('fc', 24, 'relu')
            ]
        },
        {
            'name': '自定义架构2',
            'layers': [
                ('conv', 8, 7, 'sigmoid'),
                ('pool', 2, 'max'),
                ('conv', 16, 5, 'tanh'),
                ('conv', 32, 3, 'relu'),
                ('pool', 2, 'max'),
                ('fc', 100, 'relu')
            ]
        }
    ]
    
    for config in custom_configs:
        print(f"\n构建 {config['name']}...")
        
        # 创建模型
        model = CustomizableConvolutionalNeuralNetwork(
            input_shape=(32, 32, 3),
            num_classes=10,
            learning_rate=0.01
        )
        
        # 添加层
        for layer_info in config['layers']:
            if layer_info[0] == 'conv':
                _, num_filters, kernel_size, activation = layer_info
                model.add_conv_layer(num_filters=num_filters, kernel_size=kernel_size, 
                                   padding=kernel_size//2, activation=activation)
            elif layer_info[0] == 'pool':
                _, pool_size, pool_type = layer_info
                model.add_pool_layer(pool_size=pool_size, stride=pool_size, pool_type=pool_type)
            elif layer_info[0] == 'fc':
                _, num_neurons, activation = layer_info
                model.add_fc_layer(num_neurons=num_neurons, activation=activation)
        
        # 构建模型
        model.build_model()
        
        print(f"{config['name']} 构建完成！")

if __name__ == "__main__":
    print("可自定义CNN训练示例")
    print("本示例展示了如何使用CustomizableCNN构建和训练不同架构的神经网络")
    print("\n注意：为了快速演示，使用了随机生成的示例数据")
    print("在实际应用中，请使用真实的CIFAR-10数据集")
    
    # 运行示例
    try:
        # 示例1：简单CNN
        model1 = example_1_simple_cnn()
        
        # 示例2：深层CNN
        model2 = example_2_deep_cnn()
        
        # 示例3：不同激活函数
        model3 = example_3_different_activations()
        
        # 架构比较
        compare_architectures()
        
        # 自定义架构构建器
        custom_architecture_builder()
        
    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        print("这可能是由于内存不足或其他系统限制造成的")
    
    print("\n" + "="*60)
    print("训练示例完成！")
    print("="*60)
    print("\n主要特性总结：")
    print("1. ✅ 可自定义卷积层参数（滤波器数量、核大小、步长、填充）")
    print("2. ✅ 可自定义池化层参数（池化大小、步长、池化类型）")
    print("3. ✅ 可自定义全连接层参数（神经元数量、激活函数）")
    print("4. ✅ 支持多种激活函数（ReLU、Sigmoid、Tanh）")
    print("5. ✅ 支持不同池化类型（最大池化、平均池化）")
    print("6. ✅ 自动计算网络参数数量和输出形状")
    print("7. ✅ 完整的训练和评估流程")
    print("8. ✅ 训练历史可视化")
    print("\n使用方法：")
    print("1. 创建 CustomizableConvolutionalNeuralNetwork 实例")
    print("2. 使用 add_conv_layer(), add_pool_layer(), add_fc_layer() 添加层")
    print("3. 调用 build_model() 构建模型")
    print("4. 使用 train() 方法训练模型")
    print("5. 使用 evaluate() 方法评估模型")
