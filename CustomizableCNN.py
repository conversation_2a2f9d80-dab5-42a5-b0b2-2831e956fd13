import numpy as np
import matplotlib.pyplot as plt
import pickle
import os
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

class CustomizableConvolutionalNeuralNetwork:
    def __init__(self, input_shape=(32, 32, 3), num_classes=10, learning_rate=0.001):
        """
        初始化可自定义的卷积神经网络

        参数:
        input_shape -- 输入图像形状 (height, width, channels)
        num_classes -- 分类类别数
        learning_rate -- 学习率
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.learning_rate = learning_rate

        # 网络层配置
        self.conv_layers = []
        self.pool_layers = []
        self.fc_layers = []

        # 网络参数
        self.parameters = {}

        # 训练历史
        self.train_loss_history = []
        self.train_acc_history = []
        self.val_loss_history = []
        self.val_acc_history = []

        # 当前特征图尺寸（用于计算全连接层输入大小）
        self.current_shape = list(input_shape)

    def add_conv_layer(self, num_filters, kernel_size=3, stride=1, padding=1, activation='relu'):
        """
        添加卷积层

        参数:
        num_filters -- 卷积核数量
        kernel_size -- 卷积核大小
        stride -- 步长
        padding -- 填充
        activation -- 激活函数类型
        """
        layer_config = {
            'type': 'conv',
            'num_filters': num_filters,
            'kernel_size': kernel_size,
            'stride': stride,
            'padding': padding,
            'activation': activation,
            'input_channels': self.current_shape[2]
        }

        self.conv_layers.append(layer_config)

        # 更新当前特征图尺寸
        if padding == 'same' or padding >= kernel_size // 2:
            # 保持尺寸不变（假设padding='same'）
            self.current_shape[0] = (self.current_shape[0] + 2 * padding - kernel_size) // stride + 1
            self.current_shape[1] = (self.current_shape[1] + 2 * padding - kernel_size) // stride + 1
        else:
            self.current_shape[0] = (self.current_shape[0] + 2 * padding - kernel_size) // stride + 1
            self.current_shape[1] = (self.current_shape[1] + 2 * padding - kernel_size) // stride + 1

        self.current_shape[2] = num_filters

        print(f"添加卷积层: {num_filters}个{kernel_size}x{kernel_size}卷积核, 输出形状: {self.current_shape}")

        return self

    def add_pool_layer(self, pool_size=2, stride=2, pool_type='max'):
        """
        添加池化层

        参数:
        pool_size -- 池化窗口大小
        stride -- 步长
        pool_type -- 池化类型 ('max' 或 'avg')
        """
        layer_config = {
            'type': 'pool',
            'pool_size': pool_size,
            'stride': stride,
            'pool_type': pool_type
        }

        self.pool_layers.append(layer_config)

        # 更新当前特征图尺寸
        self.current_shape[0] = (self.current_shape[0] - pool_size) // stride + 1
        self.current_shape[1] = (self.current_shape[1] - pool_size) // stride + 1

        print(f"添加池化层: {pool_size}x{pool_size} {pool_type}池化, 输出形状: {self.current_shape}")

        return self

    def add_fc_layer(self, num_neurons, activation='relu'):
        """
        添加全连接层

        参数:
        num_neurons -- 神经元数量
        activation -- 激活函数类型
        """
        layer_config = {
            'type': 'fc',
            'num_neurons': num_neurons,
            'activation': activation
        }

        self.fc_layers.append(layer_config)

        print(f"添加全连接层: {num_neurons}个神经元, 激活函数: {activation}")

        return self

    def build_model(self):
        """
        构建模型并初始化参数
        """
        print("\n构建模型...")

        # 初始化卷积层参数
        input_channels = self.input_shape[2]

        for i, layer in enumerate(self.conv_layers):
            layer_name = f'conv_{i+1}'

            # 权重初始化
            fan_in = layer['kernel_size'] * layer['kernel_size'] * input_channels
            std = np.sqrt(2.0 / fan_in)  # He初始化

            self.parameters[f'{layer_name}_weights'] = np.random.randn(
                layer['kernel_size'], layer['kernel_size'],
                input_channels, layer['num_filters']
            ) * std

            self.parameters[f'{layer_name}_bias'] = np.zeros((layer['num_filters'],))

            input_channels = layer['num_filters']

        # 计算展平后的特征数量
        flattened_size = self.current_shape[0] * self.current_shape[1] * self.current_shape[2]

        # 初始化全连接层参数
        input_size = flattened_size

        for i, layer in enumerate(self.fc_layers):
            layer_name = f'fc_{i+1}'

            # 权重初始化
            std = np.sqrt(2.0 / input_size)  # He初始化

            self.parameters[f'{layer_name}_weights'] = np.random.randn(
                input_size, layer['num_neurons']
            ) * std

            self.parameters[f'{layer_name}_bias'] = np.zeros((layer['num_neurons'],))

            input_size = layer['num_neurons']

        # 输出层
        if len(self.fc_layers) > 0:
            last_fc_size = self.fc_layers[-1]['num_neurons']
        else:
            last_fc_size = flattened_size

        std = np.sqrt(1.0 / last_fc_size)  # Xavier初始化
        self.parameters['output_weights'] = np.random.randn(last_fc_size, self.num_classes) * std
        self.parameters['output_bias'] = np.zeros((self.num_classes,))

        print("模型构建完成！")
        self._print_model_summary()

        return self

    def _print_model_summary(self):
        """打印模型结构摘要"""
        print("\n=== 模型结构摘要 ===")
        print(f"输入形状: {self.input_shape}")

        current_shape = list(self.input_shape)
        layer_count = 0

        # 卷积层和池化层
        conv_idx = 0
        pool_idx = 0

        # 假设卷积层和池化层交替出现
        total_conv_pool_layers = len(self.conv_layers) + len(self.pool_layers)

        for i in range(total_conv_pool_layers):
            if conv_idx < len(self.conv_layers) and (pool_idx >= len(self.pool_layers) or i % 2 == 0):
                # 卷积层
                layer = self.conv_layers[conv_idx]
                layer_count += 1

                # 计算输出形状
                h_out = (current_shape[0] + 2 * layer['padding'] - layer['kernel_size']) // layer['stride'] + 1
                w_out = (current_shape[1] + 2 * layer['padding'] - layer['kernel_size']) // layer['stride'] + 1
                current_shape = [h_out, w_out, layer['num_filters']]

                print(f"{layer_count:2d}. 卷积层: {layer['num_filters']}个{layer['kernel_size']}x{layer['kernel_size']}卷积核, "
                      f"步长={layer['stride']}, 填充={layer['padding']}, 激活={layer['activation']}")
                print(f"    输出形状: {current_shape}")

                conv_idx += 1

            elif pool_idx < len(self.pool_layers):
                # 池化层
                layer = self.pool_layers[pool_idx]
                layer_count += 1

                # 计算输出形状
                h_out = (current_shape[0] - layer['pool_size']) // layer['stride'] + 1
                w_out = (current_shape[1] - layer['pool_size']) // layer['stride'] + 1
                current_shape = [h_out, w_out, current_shape[2]]

                print(f"{layer_count:2d}. 池化层: {layer['pool_size']}x{layer['pool_size']} {layer['pool_type']}池化, "
                      f"步长={layer['stride']}")
                print(f"    输出形状: {current_shape}")

                pool_idx += 1

        # 展平层
        if len(self.fc_layers) > 0:
            layer_count += 1
            flattened_size = current_shape[0] * current_shape[1] * current_shape[2]
            print(f"{layer_count:2d}. 展平层: {flattened_size}个特征")

        # 全连接层
        for i, layer in enumerate(self.fc_layers):
            layer_count += 1
            print(f"{layer_count:2d}. 全连接层: {layer['num_neurons']}个神经元, 激活={layer['activation']}")

        # 输出层
        layer_count += 1
        print(f"{layer_count:2d}. 输出层: {self.num_classes}个神经元, 激活=softmax")

        # 计算参数数量
        total_params = 0
        for param_name, param in self.parameters.items():
            total_params += param.size

        print(f"\n总参数数量: {total_params:,}")
        print("=" * 50)

    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)

    def relu_derivative(self, x):
        """ReLU导数"""
        return (x > 0).astype(float)

    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

    def sigmoid_derivative(self, x):
        """Sigmoid导数"""
        s = self.sigmoid(x)
        return s * (1 - s)

    def tanh(self, x):
        """Tanh激活函数"""
        return np.tanh(x)

    def tanh_derivative(self, x):
        """Tanh导数"""
        return 1 - np.power(np.tanh(x), 2)

    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)

    def apply_activation(self, x, activation):
        """应用激活函数"""
        if activation == 'relu':
            return self.relu(x)
        elif activation == 'sigmoid':
            return self.sigmoid(x)
        elif activation == 'tanh':
            return self.tanh(x)
        elif activation == 'linear' or activation is None:
            return x
        else:
            raise ValueError(f"不支持的激活函数: {activation}")

    def apply_activation_derivative(self, x, activation):
        """应用激活函数导数"""
        if activation == 'relu':
            return self.relu_derivative(x)
        elif activation == 'sigmoid':
            return self.sigmoid_derivative(x)
        elif activation == 'tanh':
            return self.tanh_derivative(x)
        elif activation == 'linear' or activation is None:
            return np.ones_like(x)
        else:
            raise ValueError(f"不支持的激活函数: {activation}")

    def conv2d(self, input_data, weights, bias, stride=1, padding=1):
        """
        2D卷积操作

        参数:
        input_data -- 输入数据 (batch_size, height, width, channels)
        weights -- 卷积核 (kernel_height, kernel_width, input_channels, output_channels)
        bias -- 偏置 (output_channels,)
        stride -- 步长
        padding -- 填充

        返回:
        output -- 卷积输出
        """
        batch_size, input_h, input_w, input_c = input_data.shape
        kernel_h, kernel_w, _, num_filters = weights.shape

        # 添加填充
        if padding > 0:
            input_data = np.pad(input_data,
                              ((0, 0), (padding, padding), (padding, padding), (0, 0)),
                              mode='constant')

        # 计算输出尺寸
        output_h = (input_data.shape[1] - kernel_h) // stride + 1
        output_w = (input_data.shape[2] - kernel_w) // stride + 1

        # 初始化输出
        output = np.zeros((batch_size, output_h, output_w, num_filters))

        # 执行卷积
        for b in range(batch_size):
            for f in range(num_filters):
                for y in range(output_h):
                    for x in range(output_w):
                        y_start = y * stride
                        y_end = y_start + kernel_h
                        x_start = x * stride
                        x_end = x_start + kernel_w

                        # 提取输入区域
                        input_region = input_data[b, y_start:y_end, x_start:x_end, :]

                        # 卷积计算
                        output[b, y, x, f] = np.sum(input_region * weights[:, :, :, f]) + bias[f]

        return output

    def max_pool2d(self, input_data, pool_size=2, stride=2):
        """
        最大池化操作

        参数:
        input_data -- 输入数据 (batch_size, height, width, channels)
        pool_size -- 池化窗口大小
        stride -- 步长

        返回:
        output -- 池化输出
        """
        batch_size, input_h, input_w, channels = input_data.shape

        output_h = (input_h - pool_size) // stride + 1
        output_w = (input_w - pool_size) // stride + 1

        output = np.zeros((batch_size, output_h, output_w, channels))

        for b in range(batch_size):
            for c in range(channels):
                for y in range(output_h):
                    for x in range(output_w):
                        y_start = y * stride
                        y_end = y_start + pool_size
                        x_start = x * stride
                        x_end = x_start + pool_size

                        # 最大池化
                        output[b, y, x, c] = np.max(input_data[b, y_start:y_end, x_start:x_end, c])

        return output

    def avg_pool2d(self, input_data, pool_size=2, stride=2):
        """
        平均池化操作

        参数:
        input_data -- 输入数据 (batch_size, height, width, channels)
        pool_size -- 池化窗口大小
        stride -- 步长

        返回:
        output -- 池化输出
        """
        batch_size, input_h, input_w, channels = input_data.shape

        output_h = (input_h - pool_size) // stride + 1
        output_w = (input_w - pool_size) // stride + 1

        output = np.zeros((batch_size, output_h, output_w, channels))

        for b in range(batch_size):
            for c in range(channels):
                for y in range(output_h):
                    for x in range(output_w):
                        y_start = y * stride
                        y_end = y_start + pool_size
                        x_start = x * stride
                        x_end = x_start + pool_size

                        # 平均池化
                        output[b, y, x, c] = np.mean(input_data[b, y_start:y_end, x_start:x_end, c])

        return output

    def forward(self, X):
        """
        前向传播

        参数:
        X -- 输入数据 (batch_size, height, width, channels)

        返回:
        output -- 网络输出
        cache -- 缓存的中间结果
        """
        cache = {}
        current_input = X

        # 卷积层和池化层的前向传播
        conv_idx = 0
        pool_idx = 0

        # 交替处理卷积层和池化层
        total_layers = len(self.conv_layers) + len(self.pool_layers)

        for i in range(total_layers):
            if conv_idx < len(self.conv_layers) and (pool_idx >= len(self.pool_layers) or i % 2 == 0):
                # 卷积层
                layer = self.conv_layers[conv_idx]
                layer_name = f'conv_{conv_idx+1}'

                # 卷积操作
                weights = self.parameters[f'{layer_name}_weights']
                bias = self.parameters[f'{layer_name}_bias']

                conv_out = self.conv2d(current_input, weights, bias,
                                     layer['stride'], layer['padding'])
                cache[f'{layer_name}_linear'] = conv_out

                # 激活函数
                activated_out = self.apply_activation(conv_out, layer['activation'])
                cache[f'{layer_name}_activated'] = activated_out

                current_input = activated_out
                conv_idx += 1

            elif pool_idx < len(self.pool_layers):
                # 池化层
                layer = self.pool_layers[pool_idx]
                layer_name = f'pool_{pool_idx+1}'

                if layer['pool_type'] == 'max':
                    pooled_out = self.max_pool2d(current_input, layer['pool_size'], layer['stride'])
                else:  # avg
                    pooled_out = self.avg_pool2d(current_input, layer['pool_size'], layer['stride'])

                cache[layer_name] = pooled_out
                current_input = pooled_out
                pool_idx += 1

        # 展平
        batch_size = current_input.shape[0]
        flattened = current_input.reshape(batch_size, -1)
        cache['flattened'] = flattened
        current_input = flattened

        # 全连接层的前向传播
        for i, layer in enumerate(self.fc_layers):
            layer_name = f'fc_{i+1}'

            weights = self.parameters[f'{layer_name}_weights']
            bias = self.parameters[f'{layer_name}_bias']

            # 线性变换
            linear_out = np.dot(current_input, weights) + bias
            cache[f'{layer_name}_linear'] = linear_out

            # 激活函数
            activated_out = self.apply_activation(linear_out, layer['activation'])
            cache[f'{layer_name}_activated'] = activated_out

            current_input = activated_out

        # 输出层
        weights = self.parameters['output_weights']
        bias = self.parameters['output_bias']

        output_linear = np.dot(current_input, weights) + bias
        cache['output_linear'] = output_linear

        output = self.softmax(output_linear)
        cache['output'] = output

        return output, cache

    def compute_loss(self, y_pred, y_true):
        """计算交叉熵损失"""
        batch_size = y_pred.shape[0]

        # 转换为one-hot编码
        y_true_onehot = np.zeros_like(y_pred)
        y_true_onehot[np.arange(batch_size), y_true] = 1

        # 计算交叉熵损失
        epsilon = 1e-15  # 防止log(0)
        y_pred_clipped = np.clip(y_pred, epsilon, 1 - epsilon)
        loss = -np.mean(np.sum(y_true_onehot * np.log(y_pred_clipped), axis=1))

        return loss

    def compute_accuracy(self, y_pred, y_true):
        """计算准确率"""
        predictions = np.argmax(y_pred, axis=1)
        return np.mean(predictions == y_true)

    def train(self, X_train, y_train, X_val, y_val, epochs=10, batch_size=32, verbose=True):
        """
        训练网络（简化版本，仅更新全连接层）

        参数:
        X_train -- 训练数据
        y_train -- 训练标签
        X_val -- 验证数据
        y_val -- 验证标签
        epochs -- 训练轮数
        batch_size -- 批次大小
        verbose -- 是否打印训练信息
        """
        n_samples = X_train.shape[0]
        n_batches = n_samples // batch_size

        print(f"开始训练，共 {epochs} 轮，每轮 {n_batches} 个批次")

        for epoch in range(epochs):
            epoch_loss = 0
            epoch_acc = 0

            # 打乱数据
            indices = np.random.permutation(n_samples)
            X_train_shuffled = X_train[indices]
            y_train_shuffled = y_train[indices]

            # 小批次训练
            for batch in range(n_batches):
                start_idx = batch * batch_size
                end_idx = start_idx + batch_size

                X_batch = X_train_shuffled[start_idx:end_idx]
                y_batch = y_train_shuffled[start_idx:end_idx]

                # 前向传播
                y_pred, cache = self.forward(X_batch)

                # 计算损失和准确率
                loss = self.compute_loss(y_pred, y_batch)
                acc = self.compute_accuracy(y_pred, y_batch)

                epoch_loss += loss
                epoch_acc += acc

                # 简化的反向传播（仅更新全连接层和输出层）
                self._backward_fc_only(X_batch, y_batch, cache)

            # 计算平均损失和准确率
            avg_loss = epoch_loss / n_batches
            avg_acc = epoch_acc / n_batches

            # 验证集评估
            val_pred, _ = self.forward(X_val)
            val_loss = self.compute_loss(val_pred, y_val)
            val_acc = self.compute_accuracy(val_pred, y_val)

            # 记录历史
            self.train_loss_history.append(avg_loss)
            self.train_acc_history.append(avg_acc)
            self.val_loss_history.append(val_loss)
            self.val_acc_history.append(val_acc)

            if verbose:
                print(f"Epoch {epoch+1}/{epochs} - "
                      f"Loss: {avg_loss:.4f} - Acc: {avg_acc:.4f} - "
                      f"Val_Loss: {val_loss:.4f} - Val_Acc: {val_acc:.4f}")

    def _backward_fc_only(self, X_batch, y_batch, cache):
        """简化的反向传播，仅更新全连接层"""
        batch_size = X_batch.shape[0]

        # 转换为one-hot编码
        y_true_onehot = np.zeros((batch_size, self.num_classes))
        y_true_onehot[np.arange(batch_size), y_batch] = 1

        # 输出层梯度
        doutput = cache['output'] - y_true_onehot

        # 输出层参数梯度
        if len(self.fc_layers) > 0:
            last_fc_output = cache[f'fc_{len(self.fc_layers)}_activated']
        else:
            last_fc_output = cache['flattened']

        dW_output = np.dot(last_fc_output.T, doutput) / batch_size
        db_output = np.mean(doutput, axis=0)

        # 更新输出层参数
        self.parameters['output_weights'] -= self.learning_rate * dW_output
        self.parameters['output_bias'] -= self.learning_rate * db_output

        # 反向传播到全连接层
        current_grad = np.dot(doutput, self.parameters['output_weights'].T)

        for i in reversed(range(len(self.fc_layers))):
            layer = self.fc_layers[i]
            layer_name = f'fc_{i+1}'

            # 激活函数梯度
            linear_out = cache[f'{layer_name}_linear']
            dactivated = current_grad * self.apply_activation_derivative(linear_out, layer['activation'])

            # 参数梯度
            if i > 0:
                prev_output = cache[f'fc_{i}_activated']
            else:
                prev_output = cache['flattened']

            dW = np.dot(prev_output.T, dactivated) / batch_size
            db = np.mean(dactivated, axis=0)

            # 更新参数
            self.parameters[f'{layer_name}_weights'] -= self.learning_rate * dW
            self.parameters[f'{layer_name}_bias'] -= self.learning_rate * db

            # 传播到前一层
            if i > 0:
                current_grad = np.dot(dactivated, self.parameters[f'{layer_name}_weights'].T)

    def predict(self, X):
        """预测"""
        y_pred, _ = self.forward(X)
        return np.argmax(y_pred, axis=1)

    def evaluate(self, X_test, y_test):
        """评估模型"""
        predictions = self.predict(X_test)

        # 计算各种指标
        accuracy = accuracy_score(y_test, predictions)

        # 分类报告
        class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer',
                      'dog', 'frog', 'horse', 'ship', 'truck']

        report = classification_report(y_test, predictions,
                                     target_names=class_names,
                                     output_dict=True)

        # 混淆矩阵
        cm = confusion_matrix(y_test, predictions)

        return {
            'accuracy': accuracy,
            'classification_report': report,
            'confusion_matrix': cm,
            'predictions': predictions
        }


def load_cifar10_data():
    """加载CIFAR-10数据集"""
    def unpickle(file):
        with open(file, 'rb') as fo:
            dict = pickle.load(fo, encoding='bytes')
        return dict

    # 检查数据目录
    data_dir = 'cifar-10-batches-py'
    if not os.path.exists(data_dir):
        print("错误：找不到CIFAR-10数据目录")
        print("请确保已解压CIFAR-10数据集到当前目录")
        return None

    # 加载训练数据
    train_data = []
    train_labels = []

    print("正在加载CIFAR-10训练数据...")
    for i in range(1, 6):
        batch_file = os.path.join(data_dir, f'data_batch_{i}')
        if os.path.exists(batch_file):
            batch = unpickle(batch_file)
            train_data.append(batch[b'data'])
            train_labels.extend(batch[b'labels'])
            print(f"  已加载 data_batch_{i}")

    # 加载测试数据
    test_file = os.path.join(data_dir, 'test_batch')
    if os.path.exists(test_file):
        test_batch = unpickle(test_file)
        test_data = test_batch[b'data']
        test_labels = test_batch[b'labels']
        print("  已加载 test_batch")
    else:
        print("错误：找不到测试数据文件")
        return None

    # 加载标签名称
    meta_file = os.path.join(data_dir, 'batches.meta')
    if os.path.exists(meta_file):
        meta = unpickle(meta_file)
        label_names = [name.decode('utf-8') for name in meta[b'label_names']]
        print(f"  标签名称：{label_names}")
    else:
        label_names = None

    # 处理数据
    train_data = np.vstack(train_data)
    train_labels = np.array(train_labels)
    test_data = np.array(test_data)
    test_labels = np.array(test_labels)

    # 重塑数据为图像格式 (N, 32, 32, 3)
    train_data = train_data.reshape(-1, 3, 32, 32).transpose(0, 2, 3, 1)
    test_data = test_data.reshape(-1, 3, 32, 32).transpose(0, 2, 3, 1)

    # 归一化到[0,1]
    train_data = train_data.astype(np.float32) / 255.0
    test_data = test_data.astype(np.float32) / 255.0

    print(f"\n数据加载完成：")
    print(f"  训练数据形状：{train_data.shape}")
    print(f"  训练标签形状：{train_labels.shape}")
    print(f"  测试数据形状：{test_data.shape}")
    print(f"  测试标签形状：{test_labels.shape}")

    return {
        'train_data': train_data,
        'train_labels': train_labels,
        'test_data': test_data,
        'test_labels': test_labels,
        'label_names': label_names
    }


def plot_training_history(model):
    """绘制训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

    # 损失曲线
    ax1.plot(model.train_loss_history, label='Training Loss')
    ax1.plot(model.val_loss_history, label='Validation Loss')
    ax1.set_title('Model Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    # 准确率曲线
    ax2.plot(model.train_acc_history, label='Training Accuracy')
    ax2.plot(model.val_acc_history, label='Validation Accuracy')
    ax2.set_title('Model Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig('custom_cnn_training_history.png', dpi=150, bbox_inches='tight')
    plt.show()


if __name__ == "__main__":
    print("=== 可自定义卷积神经网络示例 ===")

    # 示例1：简单的CNN架构
    print("\n示例1：构建简单CNN")
    model1 = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.001
    )

    # 添加网络层
    model1.add_conv_layer(num_filters=16, kernel_size=3, padding=1, activation='relu')
    model1.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    model1.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model1.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    model1.add_fc_layer(num_neurons=64, activation='relu')

    # 构建模型
    model1.build_model()

    print("\n" + "="*60)

    # 示例2：更复杂的CNN架构
    print("\n示例2：构建复杂CNN")
    model2 = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.0005
    )

    # 添加更多层
    model2.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model2.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='relu')
    model2.add_pool_layer(pool_size=2, stride=2, pool_type='max')

    model2.add_conv_layer(num_filters=64, kernel_size=3, padding=1, activation='relu')
    model2.add_conv_layer(num_filters=64, kernel_size=3, padding=1, activation='relu')
    model2.add_pool_layer(pool_size=2, stride=2, pool_type='max')

    model2.add_fc_layer(num_neurons=128, activation='relu')
    model2.add_fc_layer(num_neurons=64, activation='relu')

    # 构建模型
    model2.build_model()

    print("\n" + "="*60)

    # 示例3：使用不同激活函数的CNN
    print("\n示例3：使用不同激活函数")
    model3 = CustomizableConvolutionalNeuralNetwork(
        input_shape=(32, 32, 3),
        num_classes=10,
        learning_rate=0.001
    )

    model3.add_conv_layer(num_filters=16, kernel_size=5, padding=2, activation='tanh')
    model3.add_pool_layer(pool_size=2, stride=2, pool_type='avg')
    model3.add_conv_layer(num_filters=32, kernel_size=3, padding=1, activation='sigmoid')
    model3.add_pool_layer(pool_size=2, stride=2, pool_type='max')
    model3.add_fc_layer(num_neurons=100, activation='tanh')
    model3.add_fc_layer(num_neurons=50, activation='relu')

    # 构建模型
    model3.build_model()

    print("\n" + "="*60)
    print("\n使用说明：")
    print("1. 使用 add_conv_layer() 添加卷积层")
    print("   - num_filters: 卷积核数量")
    print("   - kernel_size: 卷积核大小")
    print("   - stride: 步长（默认1）")
    print("   - padding: 填充（默认1）")
    print("   - activation: 激活函数（'relu', 'sigmoid', 'tanh'）")
    print("\n2. 使用 add_pool_layer() 添加池化层")
    print("   - pool_size: 池化窗口大小（默认2）")
    print("   - stride: 步长（默认2）")
    print("   - pool_type: 池化类型（'max' 或 'avg'）")
    print("\n3. 使用 add_fc_layer() 添加全连接层")
    print("   - num_neurons: 神经元数量")
    print("   - activation: 激活函数")
    print("\n4. 使用 build_model() 构建并初始化模型")
    print("\n5. 使用 train() 方法训练模型")
    print("\n注意：为了演示方便，当前实现仅更新全连接层权重")
    print("完整的卷积层权重更新需要更复杂的反向传播实现")
